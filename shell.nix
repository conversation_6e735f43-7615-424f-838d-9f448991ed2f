{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build system
    xmake

    # C compiler and development tools
    gcc
    gdb
    pkg-config

    # Core libraries
    raylib
    libiconv

    # Linux system libraries (X11/Wayland support)
    xorg.libX11
    xorg.libXrandr
    xorg.libXinerama
    xorg.libXcursor
    xorg.libXi
    xorg.libXext
    xorg.libXfixes
    wayland
    wayland-protocols
    wayland-scanner
    libxkbcommon

    # Audio libraries
    alsa-lib
    pulseaudio
    pipewire

    # Additional Linux system libraries
    systemd  # for librt, libdl
    glibc    # for pthread, math libraries

    # OpenGL libraries
    libGL
    libGLU
    mesa

    # Additional development tools
    valgrind
    strace

    # Optional: Cross-compilation support for Windows
    # Uncomment if you need Windows builds
    # pkgsCross.mingwW64.stdenv.cc
    # pkgsCross.mingwW64.windows.mingw_w64
  ];

  # Environment variables
  shellHook = ''
    echo "🎵 Hachimitsu MIDI Player Development Environment (Linux)"
    echo "========================================================="
    echo "Available tools:"
    echo "  - xmake: Build system"
    echo "  - gcc: C compiler"
    echo "  - gdb: Debugger"
    echo "  - valgrind: Memory debugging"
    echo ""
    echo "Linux support:"
    echo "  - X11 and Wayland display servers"
    echo "  - ALSA, PulseAudio, and PipeWire audio"
    echo "  - OpenGL 3.3 graphics"
    echo ""
    echo "Build commands:"
    echo "  xmake config -m debug    # Configure for debug build"
    echo "  xmake config -m release  # Configure for release build"
    echo "  xmake build              # Build the project"
    echo "  xmake run                # Run the built executable"
    echo "  xmake clean              # Clean build artifacts"
    echo ""
    echo "Debug commands:"
    echo "  gdb ./bin/hmp            # Debug with GDB"
    echo "  valgrind ./bin/hmp       # Memory check with Valgrind"
    echo ""
    echo "Linux-specific notes:"
    echo "  - Supports both X11 and Wayland"
    echo "  - Audio through ALSA/PulseAudio/PipeWire"
    echo "  - OpenGL hardware acceleration"
    echo ""

    # Set up environment for raylib
    export PKG_CONFIG_PATH="${pkgs.raylib}/lib/pkgconfig:${pkgs.alsa-lib}/lib/pkgconfig:$PKG_CONFIG_PATH"

    # Ensure proper library paths for Linux
    export LD_LIBRARY_PATH="${pkgs.raylib}/lib:${pkgs.libGL}/lib:${pkgs.alsa-lib}/lib:${pkgs.pulseaudio}/lib:${pkgs.wayland}/lib:${pkgs.xorg.libX11}/lib:$LD_LIBRARY_PATH"

    # Linux-specific environment variables
    export DISPLAY=''${DISPLAY:-:0}
    export WAYLAND_DISPLAY=''${WAYLAND_DISPLAY:-wayland-0}

    # Set up XMake cache directory
    export XMAKE_GLOBALDIR="$PWD/.xmake-global"
    mkdir -p "$XMAKE_GLOBALDIR"
  '';

  # Additional environment variables for development
  NIX_CFLAGS_COMPILE = [
    "-I${pkgs.raylib}/include"
    "-I${pkgs.libGL}/include"
    "-I${pkgs.alsa-lib}/include"
    "-I${pkgs.xorg.libX11}/include"
    "-I${pkgs.wayland}/include"
    "-I${pkgs.libxkbcommon}/include"
  ];

  NIX_LDFLAGS = [
    "-L${pkgs.raylib}/lib"
    "-L${pkgs.libGL}/lib"
    "-L${pkgs.alsa-lib}/lib"
    "-L${pkgs.xorg.libX11}/lib"
    "-L${pkgs.wayland}/lib"
    "-L${pkgs.pulseaudio}/lib"
  ];
}
