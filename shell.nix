{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build system
    xmake
    
    # C compiler and development tools
    gcc
    gdb
    pkg-config
    
    # Core libraries
    raylib
    libiconv
    
    # Linux system libraries (X11/Wayland support)
    xorg.libX11
    xorg.libXrandr
    xorg.libXinerama
    xorg.libXcursor
    xorg.libXi
    wayland
    wayland-protocols
    libxkbcommon
    
    # Audio libraries
    alsa-lib
    pulseaudio
    
    # OpenGL libraries
    libGL
    libGLU
    mesa
    
    # Additional development tools
    valgrind
    strace
    
    # Optional: Cross-compilation support for Windows
    # Uncomment if you need Windows builds
    # pkgsCross.mingwW64.stdenv.cc
    # pkgsCross.mingwW64.windows.mingw_w64
  ];

  # Environment variables
  shellHook = ''
    echo "🎵 Hachimitsu MIDI Player Development Environment"
    echo "================================================"
    echo "Available tools:"
    echo "  - xmake: Build system"
    echo "  - gcc: C compiler"
    echo "  - gdb: Debugger"
    echo "  - valgrind: Memory debugging"
    echo ""
    echo "Build commands:"
    echo "  xmake config -m debug    # Configure for debug build"
    echo "  xmake config -m release  # Configure for release build"
    echo "  xmake build              # Build the project"
    echo "  xmake run                # Run the built executable"
    echo "  xmake clean              # Clean build artifacts"
    echo ""
    echo "Debug commands:"
    echo "  gdb ./bin/hmp            # Debug with GDB"
    echo "  valgrind ./bin/hmp       # Memory check with Valgrind"
    echo ""
    
    # Set up environment for raylib
    export PKG_CONFIG_PATH="${pkgs.raylib}/lib/pkgconfig:$PKG_CONFIG_PATH"
    
    # Ensure proper library paths
    export LD_LIBRARY_PATH="${pkgs.raylib}/lib:${pkgs.libGL}/lib:${pkgs.alsa-lib}/lib:$LD_LIBRARY_PATH"
    
    # Set up XMake cache directory
    export XMAKE_GLOBALDIR="$PWD/.xmake-global"
    mkdir -p "$XMAKE_GLOBALDIR"
  '';

  # Additional environment variables for development
  NIX_CFLAGS_COMPILE = [
    "-I${pkgs.raylib}/include"
    "-I${pkgs.libGL}/include"
    "-I${pkgs.alsa-lib}/include"
  ];

  NIX_LDFLAGS = [
    "-L${pkgs.raylib}/lib"
    "-L${pkgs.libGL}/lib"
    "-L${pkgs.alsa-lib}/lib"
  ];
}
