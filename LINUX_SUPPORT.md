# Linux Support for Hachimitsu MIDI Player (HMP)

## Overview

HMP now has comprehensive Linux support with native integration for modern Linux desktop environments.

## Supported Linux Features

### Display Servers
- **X11** - Traditional X Window System
- **Wayland** - Modern display protocol
- Automatic detection and optimization for both

### Audio Systems
- **ALSA** - Advanced Linux Sound Architecture (low-level)
- **PulseAudio** - Traditional Linux audio server
- **PipeWire** - Modern audio/video server
- Automatic detection and compatibility

### Desktop Environments
- GNOME
- KDE Plasma
- XFCE
- i3/Sway
- And many others

## Installation

### Using Nix (Recommended)

1. Enter the development environment:
```bash
nix-shell
```

2. Build the project:
```bash
xmake config -m release
xmake build
```

3. Run HMP:
```bash
xmake run
```

### Manual Installation

#### Dependencies

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install build-essential pkg-config \
    libraylib-dev libiconv-hook-dev \
    libx11-dev libxrandr-dev libxinerama-dev libxcursor-dev libxi-dev \
    libwayland-dev libxkbcommon-dev \
    libasound2-dev libpulse-dev \
    libgl1-mesa-dev
```

**Fedora/RHEL:**
```bash
sudo dnf install gcc make pkg-config \
    raylib-devel \
    libX11-devel libXrandr-devel libXinerama-devel libXcursor-devel libXi-devel \
    wayland-devel libxkbcommon-devel \
    alsa-lib-devel pulseaudio-libs-devel \
    mesa-libGL-devel
```

**Arch Linux:**
```bash
sudo pacman -S base-devel pkg-config \
    raylib \
    libx11 libxrandr libxinerama libxcursor libxi \
    wayland libxkbcommon \
    alsa-lib libpulse \
    mesa
```

#### Build System

Install XMake:
```bash
curl -fsSL https://xmake.io/shget.text | bash
```

## Linux-Specific Features

### File Dialog Integration
- **Zenity** (GNOME/GTK environments)
- **KDialog** (KDE/Qt environments)
- Automatic fallback to file manager

### System Notifications
- Native desktop notifications via `notify-send`
- Integration with system notification daemon

### Audio System Detection
HMP automatically detects and optimizes for your audio system:
- PipeWire (preferred for modern systems)
- PulseAudio (traditional choice)
- ALSA (direct hardware access)

### Display Server Optimization
- Wayland: Native support with proper scaling
- X11: Traditional support with all features

## Building from Source

1. Clone the repository:
```bash
git clone https://github.com/your-repo/hmp.git
cd hmp
```

2. Enter Nix shell (if using Nix):
```bash
nix-shell
```

3. Configure for Linux:
```bash
xmake config -p linux -m release
```

4. Build:
```bash
xmake build
```

5. Install (optional):
```bash
sudo xmake install
```

## Runtime Requirements

### Essential
- OpenGL 3.3 compatible graphics driver
- Audio system (ALSA/PulseAudio/PipeWire)
- Display server (X11/Wayland)

### Optional
- `zenity` or `kdialog` for file dialogs
- `notify-send` for system notifications
- Hardware-accelerated graphics driver

## Troubleshooting

### Audio Issues
1. Check audio system:
```bash
# Check if PipeWire is running
pgrep pipewire

# Check if PulseAudio is running
pgrep pulseaudio

# List ALSA devices
aplay -l
```

2. Test audio output:
```bash
speaker-test -t wav -c 2
```

### Graphics Issues
1. Check OpenGL support:
```bash
glxinfo | grep "OpenGL version"
```

2. For Wayland, ensure proper environment:
```bash
echo $WAYLAND_DISPLAY
echo $XDG_SESSION_TYPE
```

### File Dialog Issues
Install dialog tools:
```bash
# For GTK/GNOME environments
sudo apt install zenity

# For Qt/KDE environments
sudo apt install kdialog
```

## Performance Optimization

### For Low-End Systems
- Use ALSA directly (disable PulseAudio/PipeWire)
- Use X11 instead of Wayland
- Reduce visual effects in HMP settings

### For High-End Systems
- Enable hardware acceleration
- Use PipeWire for best audio quality
- Use Wayland for better scaling

## Known Issues

1. **Wayland + NVIDIA**: Some NVIDIA drivers have issues with Wayland
   - Solution: Use X11 session or update drivers

2. **Audio Latency**: High latency on some systems
   - Solution: Adjust audio buffer settings

3. **File Permissions**: MIDI files in protected directories
   - Solution: Copy files to user directory

## Contributing

Linux-specific contributions are welcome! Areas of interest:
- Additional audio system support
- Desktop environment integration
- Package manager integration
- Distribution-specific optimizations

## Support

For Linux-specific issues:
1. Check system information: Run HMP with debug output
2. Verify dependencies are installed
3. Test with different audio/display configurations
4. Report issues with system details

## System Information

HMP automatically detects and logs:
- Linux distribution
- Desktop environment
- Display server (X11/Wayland)
- Audio system (ALSA/PulseAudio/PipeWire)
- OpenGL version and capabilities

This information is displayed at startup and can help with troubleshooting.
