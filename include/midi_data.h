#ifndef MIDI_DATA_H
#define MIDI_DATA_H

#include <stdint.h>
#include <stdbool.h>

// MIDIイベントの種類を定義する列挙型
typedef enum {
    EVENT_NOTE_OFF,
    EVENT_NOTE_ON,
    EVENT_POLY_PRESSURE,
    EVENT_CONTROL_CHANGE,
    EVENT_PROGRAM_CHANGE,
    EVENT_CHANNEL_PRESSURE,
    EVENT_PITCH_BEND,
    EVENT_META,
    EVENT_SYSEX,
    EVENT_UNKNOWN
} MidiEventType;

// トラック構造体
typedef struct {
    uint8_t *head;
    uint64_t Tick;
    uint8_t RunningStatus;
    bool Ended;
} Track;

// MIDIファイル構造体 - シンプル化
typedef struct {
    bool Running;
    uint8_t *Data;
    int DataLength;
    int TrackCount;
    int Division;
    unsigned long long LastTime;
    Track *Tracks;
    uint64_t CurrentTick;     // 現在のティック位置
    uint64_t TotalTicks;      // 総ティック数
    long long CurrentTempo;   // 現在のテンポ（マイクロ秒/拍）
} MIDIFile;

// 外部参照用
extern MIDIFile *gMidiFile;

#endif // MIDI_DATA_H
