#ifndef LYRICS_DISPLAY_H
#define LYRICS_DISPLAY_H

#include <stdbool.h>
#include <stdint.h>
#include <pthread.h>

// 最大イベント数の定義
#define MAX_LYRICS_EVENTS 5000
#define MAX_MARKER_EVENTS 1000
#define MAX_EVENT_TEXT_LENGTH 256

// 歌詞/マーカーイベントの構造体
typedef struct {
    uint64_t tick;           // イベントが発生するティック位置
    char text[MAX_EVENT_TEXT_LENGTH]; // イベントのテキスト
    bool displayed;          // 既に表示されたか
} TextEvent;

typedef struct {
    bool initialized;
    pthread_mutex_t mutex;

    // 表示設定
    bool showLyrics;
    bool showMarkers;
    int fadeTime;
    int fontSize;

    // イベントデータ
    TextEvent lyrics[MAX_LYRICS_EVENTS];
    int lyricsCount;
    TextEvent markers[MAX_MARKER_EVENTS];
    int markerCount;

    // 現在表示中のテキスト
    char currentLyric[MAX_EVENT_TEXT_LENGTH];
    char currentMarker[MAX_EVENT_TEXT_LENGTH];
} LyricsDisplay;

// グローバルインスタンス
extern LyricsDisplay gLyricsDisplay;

// 初期化と解放
void initLyricsDisplay();
void cleanupLyricsDisplay();

// イベント追加
void addLyricEvent(uint64_t tick, const char* text);
void addMarkerEvent(uint64_t tick, const char* text);

// 表示処理
void updateLyricsDisplay(uint64_t currentTick);
void drawLyricsDisplay();

// コントロール関数
void toggleLyricsDisplay();
void toggleMarkersDisplay();
void resetLyricsEvents();

// フォントサイズ変更関数
void changeLyricsFontSize(int size);

#endif // LYRICS_DISPLAY_H
