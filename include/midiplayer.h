#ifndef MIDIPLAYER_H
#define MIDIPLAYER_H

#include <stdio.h>
#include <stdbool.h>
#include <stdint.h>

#include "midi_header.h"
#include "midi_data.h"
#include "OmniMIDI.h"

// MIDIファイルのロード状態を確認する関数
bool isMidiLoaded(void);

// 現在のBPMを取得する
float getCurrentBPM(void);

// BPM倍率を取得する
float getBpmMultiplier(void);

// MIDIの時間文字列を取得する
void getMidiTimeString(char* buffer, int bufferSize, bool isTotal);

// BPM情報を取得する関数
void getMidiBpmInfo(float* minBpm, float* maxBpm, float* avgBpm);

// プログラムチェンジ情報を取得する関数
uint8_t getCurrentProgram(uint8_t channel);
const char* getCurrentInstrumentName(uint8_t channel); // 楽器名を取得

// 外部に公開する関数
void *playMidiFile(void *context);
void *loadMidiFile(void *context);
void delayExecution(long nanoseconds);
long long get_ns();
void sleepNanos(long long nanos);
void toggleMidiPlayPause();  // 再生/一時停止の切り替え
bool isMidiPaused();        // 一時停止状態かどうかを取得

// 内部実装のためのヘッダーファイル
// 外部からは直接使わないように警告を入れる
#ifdef INTERNAL_MIDIPLAYER_IMPLEMENTATION
#include "midiplayer/utils.h"
#include "midiplayer/player.h"
#include "midiplayer/loader.h"
#endif

#endif
