#ifndef MIDI_UTILS_H
#define MIDI_UTILS_H

#include <stdint.h>
#include <stdbool.h>

#ifdef _WIN32
// Windows環境では必要なヘッダーをインクルード
#include <windows.h>
// もしくは明示的に定義する場合:
// #ifndef MAX_PATH
// #define MAX_PATH 260
// #endif

// Windows環境でワイド文字パスを保存するためのグローバル変数
extern wchar_t gWideFilePath[MAX_PATH];
extern bool gUseWidePath;
#endif

// バイトスワップ関数
uint16_t swap_uint16(uint16_t val);
uint32_t swap_uint32(uint32_t val);

// MIDI関連の関数
uint32_t create_midi_message(uint8_t status, uint8_t channel, uint8_t data1, uint8_t data2);
void send_note_on(uint8_t channel, uint8_t note, uint8_t velocity);
void send_note_off(uint8_t channel, uint8_t note, uint8_t velocity);
void send_program_change(uint8_t channel, uint8_t program); // プログラムチェンジ関数追加

// SysExメッセージを送信
void send_sysex_message(uint8_t* data, uint32_t length);

#endif
