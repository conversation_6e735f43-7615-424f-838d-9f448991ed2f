#ifndef OMNIMIDI_H
#define OMNIMIDI_H

#include <stdbool.h>
#include <stdint.h>

// Windowsヘッダーを直接インクルードせず、必要な場合はraylib_compat.hを介して行う
/* #ifdef _WIN32
#include <windows.h>
#endif */

// OmniMIDI/KDMAPI関数ポインタの型定義
typedef bool (*InitializeKDMAPIStream_t)(void);
typedef bool (*TerminateKDMAPIStream_t)(void);
typedef void (*ResetKDMAPIStream_t)(void);
typedef bool (*IsKDMAPIAvailable_t)(void);
typedef bool (*ReturnKDMAPIVer_t)(uint32_t *Major, uint32_t *Minor, uint32_t *Build, uint32_t *Revision);
typedef void (*SendDirectData_t)(uint32_t msg);
typedef void (*SendLongData_t)(uint8_t* data, uint32_t size);  // SysEx送信用の関数ポインタ型を追加

// OmniMIDIライブラリをロード
void* LoadOmniMIDI(void);

// OmniMIDI関数ポインタを設定
bool InitOmniMIDIFunctions(void* handle);

// グローバル関数ポインタ（外部から使用可能）
extern InitializeKDMAPIStream_t KDMInit;
extern TerminateKDMAPIStream_t KDMStop;
extern ResetKDMAPIStream_t KDMReset;
extern IsKDMAPIAvailable_t KDMAPIStatus;
extern ReturnKDMAPIVer_t KDMAPIVer;
extern SendDirectData_t KDMSendDirectData;
extern SendLongData_t KDMSendLongData;  // 関数ポインタを正しく宣言（externで）

#endif
