#ifndef PIANO_VISUALIZER_H
#define PIANO_VISUALIZER_H

#include <raylib.h>
#include "midi_visualizer.h"

// ピアノキーの定数
#define TOTAL_KEYS 128
#define LOWEST_NOTE 0
#define OCTAVE_NOTES 12

// ノートビジュアライザー用の定数
#define MAX_NOTE_HISTORY 5000  // 履歴数を減らす（100000→5000）
#define NOTE_SPEED 2000.0f     // ノートが上に移動する速度（ピクセル/秒）
#define NOTE_CLEANUP_THRESHOLD 5000  // クリーンアップのしきい値を減らす

// ピアノキーの変数（動的に計算するため定数ではなく変数）
extern int whiteKeyWidth;
extern int whiteKeyHeight;
extern int blackKeyWidth;
extern int blackKeyHeight;
extern int keyboardStartX;
extern int keyboardStartY;

// 白鍵と黒鍵のパターン (オクターブ内での位置)
extern const int KEY_PATTERN[OCTAVE_NOTES];

// ノート履歴を格納する構造体
typedef struct {
    int note;           // ノート番号
    int channel;        // MIDIチャンネル
    float position;     // 現在の垂直位置
    float startTime;    // 開始時間
    bool active;        // アクティブかどうか
    bool used;          // このスロットが使用中かどうか
    float endPosition;  // ノートがオフになった時の位置（長さ計算用）
} NoteVisual;

// キーの状態を保持する構造体
typedef struct {
    float targetHeight;   // 目標の高さ
    float currentHeight;  // 現在の高さ
    float colorBlend;     // 色のブレンド係数 (0.0=元の色、1.0=チャンネル色)
    bool isActive;        // アクティブ状態
    int lastChannel;      // 最後にアクティブだったチャンネル
} KeyState;

// 外部から参照される変数
extern NoteVisual noteHistory[MAX_NOTE_HISTORY];
extern int nextNoteIndex;
extern KeyState keyStates[TOTAL_KEYS];
extern Camera3D camera;
extern Color channelColors[16];

// 各チャンネルのピッチベンド値を保持する配列 (0-16383, 中央値=8192)
// piano_visualizer.cで実際に定義される
extern int channelPitchBends[16];

// カメラのプリセット（0=近い, 1=遠い, 2=ピアノロール）
extern int currentCameraPreset;

// 関数プロトタイプ
void updateChannelPitchBend(int channel, int value);
int countWhiteKeys(void);
void updatePianoSizes(void);
int getNoteX(int note);
int getNoteWidth(int note);
void updateNoteData(void);
void initCamera(void);
void initKeyStates(void);
void updateKeyStates(void);
void draw3DPianoKeyboard(void);
void updateCamera(void);
void switchCameraPreset(int preset);

// ピアノビジュアライザーの状態をリセットする関数
void resetPianoVisualizer(void);

// プリレンダリング関連の関数（空の実装に置き換え済み）
void initNotePrerendering(void);
void updatePrerenderedNotes(void);
void clearPrerenderedNotes(void);
void preloadNotesFromMidi(int startTimeMs, int durationMs);
void drawPianoRollFromPrerenderedNotes(void);
int getPrerenderedNoteCount(void);
int getActivePrerenderedNoteCount(void);
bool importExternalPrerenderingData(void);
void sortPrerenderedNotes(void);

#endif // PIANO_VISUALIZER_H
