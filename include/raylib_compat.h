#ifndef RAYLIB_COMPAT_H
#define RAYLIB_COMPAT_H

/*
 * WindowsとRaylibのAPI衝突を解決するヘッダーファイル
 */

// WindowsとRaylibの競合を解決するためのマクロ定義
#ifdef _WIN32
    // 最小限のWindows APIのみを含める
    #define WIN32_LEAN_AND_MEAN
    #define NOMINMAX

    // WindowsとRaylibの競合する関数名をリネーム
    #define Rectangle RaylibRectangle
    #define CloseWindow RaylibCloseWindow
    #define DrawText RaylibDrawText
    #define ShowCursor RaylibShowCursor
    #define LoadImage RaylibLoadImage
    #define DrawTextEx RaylibDrawTextEx
    #define PlaySound RaylibPlaySound

    // その他の競合する可能性のある関数名も必要に応じて追加

    // Windowsヘッダーをインクルード
    #include <windows.h>

    // 元の関数名を復元
    #undef Rectangle
    #undef CloseWindow
    #undef DrawText
    #undef ShowCursor
    #undef LoadImage
    #undef DrawTextEx
    #undef PlaySound
#endif

// Raylibのヘッダーをインクルード
#include <raylib.h>

#endif // RAYLIB_COMPAT_H
