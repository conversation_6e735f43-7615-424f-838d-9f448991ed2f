#ifndef LINUX_UTILS_H
#define LINUX_UTILS_H

#ifdef __linux__

// Linux固有のオーディオシステム列挙
typedef enum {
    AUDIO_UNKNOWN = 0,
    AUDIO_ALSA,
    AUDIO_PULSEAUDIO,
    AUDIO_PIPEWIRE
} AudioSystem;

// Linux固有のディスプレイサーバー列挙
typedef enum {
    DISPLAY_UNKNOWN = 0,
    DISPLAY_X11,
    DISPLAY_WAYLAND
} DisplayServer;

// Linux固有のシステム情報構造体
typedef struct {
    AudioSystem audio_system;
    DisplayServer display_server;
    char distribution[128];
    char desktop_environment[64];
} LinuxSystemInfo;

// 関数宣言

/**
 * Linux固有のファイルダイアログを開く
 * @param title ダイアログのタイトル
 * @param filter ファイルフィルター
 * @return 選択されたファイルパス（要free）、キャンセル時はNULL
 */
char* linux_open_file_dialog(const char* title, const char* filter);

/**
 * Linux固有のシステム通知を送信
 * @param title 通知のタイトル
 * @param message 通知メッセージ
 * @param icon アイコン名またはパス（オプション）
 */
void linux_send_notification(const char* title, const char* message, const char* icon);

/**
 * Linux固有のオーディオシステムを検出
 * @return 検出されたオーディオシステム
 */
AudioSystem linux_detect_audio_system();

/**
 * Linux固有のオーディオシステム名を取得
 * @param system オーディオシステム
 * @return システム名
 */
const char* linux_get_audio_system_name(AudioSystem system);

/**
 * Linux固有のディスプレイサーバーを検出
 * @return 検出されたディスプレイサーバー
 */
DisplayServer linux_detect_display_server();

/**
 * Linux固有のディスプレイサーバー名を取得
 * @param server ディスプレイサーバー
 * @return サーバー名
 */
const char* linux_get_display_server_name(DisplayServer server);

/**
 * Linux固有のシステム情報を取得
 * @param info システム情報を格納する構造体
 */
void linux_get_system_info(LinuxSystemInfo* info);

/**
 * Linux固有のシステム情報を表示
 */
void linux_print_system_info();

#endif // __linux__

#endif // LINUX_UTILS_H
