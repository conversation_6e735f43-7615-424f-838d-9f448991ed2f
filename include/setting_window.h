#ifndef SETTING_WINDOW_H
#define SETTING_WINDOW_H

#include <raylib.h>
#include <stdbool.h>

// 設定ウィンドウの表示状態
extern bool showSettingWindow;

// ピッチベンド機能の有効/無効設定
extern bool enablePitchBend;

// キーボード2D表示の有効/無効設定
extern bool enableKeyboardDisplay;

// 各機能のキーボードショートカット設定用
extern bool enableLyricsToggle;       // F4: 歌詞表示切替
extern bool enableMarkersToggle;      // F5: マーカー表示切替

// 設定ウィンドウの表示/非表示を切り替える
void toggleSettingWindow(void);

// 設定ウィンドウの描画
void drawSettingWindow(void);

#endif // SETTING_WINDOW_H
