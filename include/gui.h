#ifndef GUI_H
#define GUI_H

#include <stdbool.h>
#include <raylib.h>

// ウィンドウタイトルを更新する関数
void updateWindowTitle(const char* filename);

// MIDIロード後にウィンドウタイトルを安全に更新するため
void setWindowTitleUpdateFlag(const char* filename);
bool hasPendingTitleUpdate(void);
const char* getPendingTitleFilename(void);
void clearWindowTitleUpdateFlag(void);

// GUIスレッドを実行する関数
void* gui_thread_function();

// 入力ブロック状態を取得する関数
bool shouldBlockInput();

// 日本語フォント関連
Font getJapaneseFont();
void initJapaneseFont(int fontSize);
void unloadJapaneseFont();

// 外部から参照するグローバル変数の宣言
extern bool IS_QUIT;

// 埋め込みファイルの参照 fonts/NotoSansJP-SemiBold.ttf
extern const unsigned char notoSansJpSemiBoldTtf[];
extern const int notoSansJpSemiBoldTtf_len;

// 前方宣言 - 関数が使用される前に宣言を追加
void resetNoteVisualization(void);

#endif // GUI_H
