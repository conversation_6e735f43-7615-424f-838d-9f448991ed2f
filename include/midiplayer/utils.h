#ifndef MIDIPLAYER_UTILS_H
#define MIDIPLAYER_UTILS_H

#include <stdint.h>
#include <stdbool.h>

// テンポマップ構造体の定義
typedef struct {
    uint64_t tick;
    long long tempo;
} TempoChange;

typedef struct {
    <PERSON><PERSON><PERSON>hang<PERSON> changes[1024]; // 最大1024個のテンポ変更を保存
    int count;
} TempoMap;

// プログラムチェンジ状態管理のための構造体
typedef struct {
    uint8_t programs[16];       // 各チャンネルの現在のプログラム番号
    bool hasChanged[16];        // プログラムが変更されたかのフラグ
    uint64_t lastChangeTick[16]; // 最後に変更されたティック位置
} ProgramChangeState;

// グローバル変数宣言
extern TempoMap tempoMap;
extern ProgramChangeState programState;

// プロトタイプ宣言
void initProgramState(void);
void recordProgramChange(uint8_t channel, uint8_t program, uint64_t tick);
uint8_t getCurrentProgram(uint8_t channel);
const char* getCurrentInstrumentName(uint8_t channel);
void cleanupProgramState(void);

// テンポマップ関連の関数
void initTempoMap(void);
void addTempoChange(uint64_t tick, long long tempo);
void recordTempoChange(uint64_t tick, long long tempo);
void cleanupTempoMap(void);
void sortTempoMap(void);
void debugPrintTempoMap(void);

// 時間計測・遅延関連
long long get_ns(void);
void delayExecution(long nanoseconds);
void sleepNanos(long long nanos);

// 再生制御関連
void toggleMidiPlayPause(void);
bool isMidiPaused(void);
void setMidiPaused(bool paused);  // 追加: 一時停止状態を直接設定する関数
float getMidiProgress(void);
bool setMidiProgress(float progress);
bool isMidiLoaded(void);

// BPM関連の関数
float getCurrentBPM(void);
float getBpmMultiplier(void);
bool setPlaybackBPM(float multiplier);
float getAverageBPM(void);
void getMidiBpmInfo(float *minBpm, float *maxBpm, float *avgBpm);

// 時間計算関連
void initializeTimeCalculation(void);
int getCurrentTimeMs(void);
int getCurrentMidiTimeMs(void);  // BPM倍率に影響されない
int getMidiTotalTimeMs(void);
int getTotalMidiTimeMs(void);    // BPM倍率に影響されない
uint64_t getTickFromTime(int timeMs);
void getMidiTimeString(char* buffer, int bufferSize, bool getTotalTime);

// 指定ティックまでの経過時間を計算（テンポマップを考慮）
int calculateElapsedTime(uint64_t currentTick);

// ノート情報の前処理と更新用の関数
void preprocessNoteInfo(uint8_t* data, uint32_t length, uint64_t tick);
void updateNoteTimings(uint64_t tick, long long tempo);

// 相対時間モード関連の関数
void setRelativeTimeMode(bool enabled);
bool isRelativeTimeMode();
void resetRelativeTimePosition();

// 内部実装関数（utils.c内部でのみ使用）
#ifdef INTERNAL_MIDIPLAYER_IMPLEMENTATION
// MIDIファイルの総再生時間をシミュレートして計算
double simulateMidiFileDuration(void);

// 指定したティック位置でのテンポを取得
long long getCurrentTempoAtTick(uint64_t tick);
#endif

#endif // MIDIPLAYER_UTILS_H
