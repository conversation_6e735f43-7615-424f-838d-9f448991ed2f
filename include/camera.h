#ifndef CAMERA_H
#define CAMERA_H

#include <raylib.h>
#include <stdbool.h>

// カメラ関連の定数と変数
extern Camera3D camera;
extern Vector3 cameraPosition;
extern Vector3 cameraTarget;
extern Vector3 cameraUp;
extern bool isMouseMove;
extern bool is88KeyMode; // 88鍵盤モードのフラグ

// 追加: アダプティブビュー関連の変数
extern bool isAdaptiveViewEnabled; // アダプティブビュー機能の有効/無効
extern float cameraTransitionProgress; // カメラトランジションの進行度 (0.0-1.0)
extern bool isTransitioning; // 現在トランジション中かどうか
extern bool targetIs128KeyMode; // トランジションの目標モードを追加

// カメラの初期化、更新、デバッグ機能
void initCamera(void);
void updateCamera(void);
void drawCameraDebugInfo(void);
void resetCamera(void);
void toggle88KeyMode(void); // 既存の関数
void set88KeyMode(bool enable); // 新しい関数を追加

// 追加: アダプティブビュー関連の関数
void checkNoteRangeAndUpdateCamera(void); // ノート範囲をチェックしてカメラを更新
void updateCameraTransition(void); // カメラトランジションを更新
void toggleAdaptiveView(void); // アダプティブビュー機能のオン/オフを切り替え
bool isNoteOutsidePianoRange(void); // 88鍵範囲外のノートがあるか確認
void initAdaptiveView(void); // アダプティブビュー状態の初期化

#endif // CAMERA_H
