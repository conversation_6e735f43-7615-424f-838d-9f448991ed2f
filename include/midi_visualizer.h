#ifndef MIDI_VISUALIZER_H
#define MIDI_VISUALIZER_H

#include <stdbool.h>
#include <stdint.h>
#include <pthread.h>

// 各ノートの状態を追跡
typedef struct {
    bool active;           // ノートが現在アクティブか
    uint8_t velocity;      // ノートのベロシティ (0-127)
    uint64_t startTime;    // ノートが開始された時間
    uint64_t endTime;      // ノートが終了した時間
    bool needsUpdate;      // 更新が必要かどうか
} NoteState;

// ビジュアライザーのデータ構造
typedef struct {
    NoteState notes[16][128];  // [チャンネル][ノート番号]
    pthread_mutex_t mutex;     // データ保護用ミューテックス
    uint64_t currentTime;      // 現在の時間（視覚的同期用）
    bool initialized;          // 初期化済みフラグ
} MidiVisualizer;

// グローバルビジュアライザーインスタンス
extern MidiVisualizer gVisualizer;

// 初期化と解放
void initVisualizer();
void cleanupVisualizer();

// ノート状態の更新
void visualizerNoteOn(uint8_t channel, uint8_t note, uint8_t velocity);
void visualizerNoteOff(uint8_t channel, uint8_t note);

// 現在時刻の更新
void visualizerUpdateTime(uint64_t time);

// 現在時刻を取得する関数を追加
uint64_t getVisualizerTime();

// ビジュアライザーの状態をリセットする関数
void resetMidiVisualizer();

// ノート統計情報の取得関数（追加）
int getCurrentPolyphony(void);   // 現在の同時発音数
int getTotalNotes(void);         // 演奏されたノートの合計
int getCurrentNotes(void);       // 現在アクティブなノート数
float getNotesPerSecond(void);   // 1秒あたりのノート数（NPS）

// 新しい関数: 総ノート数を外部から設定
void setTotalNotes(int count);

#endif // MIDI_VISUALIZER_H
