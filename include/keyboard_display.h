#ifndef KEYBOARD_DISPLAY_H
#define KEYBOARD_DISPLAY_H

#include <raylib.h>
#include <stdbool.h>

// キーボード2D表示の表示状態
extern bool showKeyboardDisplay;

// キーボードの表示設定
typedef struct {
    int keyWidth;        // 白鍵の幅
    int whiteKeyHeight;  // 白鍵の高さ
    int blackKeyHeight;  // 黒鍵の高さ
    int channelHeight;   // 各チャンネルの高さ
    int startNote;       // 開始ノート番号 (通常は0か21)
    int endNote;         // 終了ノート番号 (通常は127か108)
} KeyboardDisplayConfig;

// 16チャンネル分のキーボード状態
extern bool noteState[16][128];

// 初期化関数
void initKeyboardDisplay(void);

// 16チャンネル分のキーボードを描画
void drawKeyboardDisplay(void);

// ノートオン/オフイベント処理
void keyboardNoteOn(int channel, int note);
void keyboardNoteOff(int channel, int note);

// チャンネルの色を取得（既存のチャンネル色設定があれば利用）
Color getChannelColor(int channel);

#endif // KEYBOARD_DISPLAY_H
