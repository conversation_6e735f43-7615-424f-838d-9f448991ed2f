#include "raylib_compat.h"  // Windowsヘッダーをインクルードする前にこれを追加

#ifdef _WIN32
#include <windows.h>
#else
#include <dlfcn.h>
#endif

#include <stdio.h>
#include <stdlib.h>
#include <OmniMIDI.h>

// 関数ポインタのグローバル変数を定義
InitializeKDMAPIStream_t KDMInit = NULL;
TerminateKDMAPIStream_t KDMStop = NULL;
ResetKDMAPIStream_t KDMReset = NULL;
IsKDMAPIAvailable_t KDMAPIStatus = NULL;
ReturnKDMAPIVer_t KDMAPIVer = NULL;
SendDirectData_t KDMSendDirectData = NULL;
SendLongData_t KDMSendLongData = NULL;  // KDMSendLongDataの定義を追加

// Windowsの場合
#ifdef _WIN32
const char* libPath = "OmniMIDI.dll";
#endif

// Linuxの場合
#ifdef __linux__
const char* libPath = "libOmniMIDI.so";
#endif

// MacOSの場合
#ifdef __APPLE__
const char* libPath = "./OmniMIDI.dylib";
#endif

void* LoadOmniMIDI() {
    void* handle;

#ifdef _WIN32
    // Windows用のDLLロード処理
    HMODULE hDll = LoadLibraryA(libPath);
    if (!hDll) {
        fprintf(stderr, "OmniMIDIライブラリをロードできませんでした。エラーコード: %lu\n", GetLastError());
        return NULL;
    }
    handle = (void*)hDll;
#else
    // UNIX系のdlopen処理
    handle = dlopen(libPath, RTLD_LAZY);
    if (!handle) {
        fprintf(stderr, "OmniMIDIライブラリをロードできませんでした: %s\n", dlerror());
        // 代替のパスを試す
        handle = dlopen("./libomnimidi.so", RTLD_LAZY);
        if (!handle) {
            fprintf(stderr, "OmniMIDIライブラリをロードできませんでした: %s\n", dlerror());
            return NULL;
        }
    }
#endif

    // 関数ポインタを初期化
    if (!InitOmniMIDIFunctions(handle)) {
        fprintf(stderr, "OmniMIDI関数の初期化に失敗しました\n");
#ifdef _WIN32
        FreeLibrary((HMODULE)handle);
#else
        dlclose(handle);
#endif
        return NULL;
    }

    // 初期化成功
    fprintf(stderr, "OmniMIDIライブラリが正常にロードされました\n");
    return handle;
}

bool InitOmniMIDIFunctions(void* handle) {
    if (!handle) {
        fprintf(stderr, "無効なライブラリハンドル\n");
        return false;
    }

#ifdef _WIN32
    // Windows用の関数ポインタ取得
    // 関数型キャスト警告を抑制するためのプラグマ（MSVCのみ）
    #ifdef _MSC_VER
    #pragma warning(push)
    #pragma warning(disable: 4055)  // データポインタから関数ポインタへの警告を抑制
    #endif

    // GCC/Clangの場合は警告を一時的に無効化
    #if defined(__GNUC__) || defined(__clang__)
    #pragma GCC diagnostic push
    #pragma GCC diagnostic ignored "-Wcast-function-type"
    #endif

    KDMInit = (InitializeKDMAPIStream_t)GetProcAddress((HMODULE)handle, "InitializeKDMAPIStream");
    if (!KDMInit) {
        fprintf(stderr, "InitializeKDMAPIStream関数のロードエラー: %lu\n", GetLastError());
        return false;
    }

    KDMStop = (TerminateKDMAPIStream_t)GetProcAddress((HMODULE)handle, "TerminateKDMAPIStream");
    if (!KDMStop) {
        fprintf(stderr, "TerminateKDMAPIStream関数のロードエラー: %lu\n", GetLastError());
        return false;
    }

    // オプション関数のポインタを取得
    KDMReset = (ResetKDMAPIStream_t)GetProcAddress((HMODULE)handle, "ResetKDMAPIStream");
    KDMAPIStatus = (IsKDMAPIAvailable_t)GetProcAddress((HMODULE)handle, "IsKDMAPIAvailable");
    KDMAPIVer = (ReturnKDMAPIVer_t)GetProcAddress((HMODULE)handle, "ReturnKDMAPIVer");
    KDMSendDirectData = (SendDirectData_t)GetProcAddress((HMODULE)handle, "SendDirectData");
    KDMSendLongData = (SendLongData_t)GetProcAddress((HMODULE)handle, "SendLongData");  // SysEx関数の初期化

    // 警告抑制を元に戻す
    #ifdef _MSC_VER
    #pragma warning(pop)
    #endif

    #if defined(__GNUC__) || defined(__clang__)
    #pragma GCC diagnostic pop
    #endif
#else
    // UNIX系の関数ポインタ取得
    dlerror(); // エラーメッセージをクリア

    KDMInit = (InitializeKDMAPIStream_t)dlsym(handle, "InitializeKDMAPIStream");
    char* error = dlerror();
    if (error != NULL) {
        fprintf(stderr, "InitializeKDMAPIStream関数のロードエラー: %s\n", error);
        return false;
    }

    KDMStop = (TerminateKDMAPIStream_t)dlsym(handle, "TerminateKDMAPIStream");
    error = dlerror();
    if (error != NULL) {
        fprintf(stderr, "TerminateKDMAPIStream関数のロードエラー: %s\n", error);
        return false;
    }

    // オプション関数のポインタを取得
    KDMReset = (ResetKDMAPIStream_t)dlsym(handle, "ResetKDMAPIStream");
    KDMAPIStatus = (IsKDMAPIAvailable_t)dlsym(handle, "IsKDMAPIAvailable");
    KDMAPIVer = (ReturnKDMAPIVer_t)dlsym(handle, "ReturnKDMAPIVer");
    KDMSendDirectData = (SendDirectData_t)dlsym(handle, "SendDirectData");
    KDMSendLongData = (SendLongData_t)dlsym(handle, "SendLongData");  // SysEx関数の初期化
#endif

    // SendDirectDataは必須であることを確認
    if (!KDMSendDirectData) {
        fprintf(stderr, "SendDirectData関数が見つかりません\n");
        return false;
    }

    // SysEx関数がない場合は警告を出すが、致命的ではない
    if (!KDMSendLongData) {
        fprintf(stderr, "Warning: SysEx function (SendLongData) not available in OmniMIDI\n");
        // 続行可能
    }

    // 基本的な機能が利用可能かチェック
    if (KDMAPIStatus && !KDMAPIStatus()) {
        fprintf(stderr, "KDMAPIは利用できません\n");
        return false;
    }

    fprintf(stderr, "全てのOmniMIDI関数が正常にロードされました\n");
    return true;
}
