#include "lyrics_display.h"
#include "midi_data.h"
#include "gui.h" // フォント取得のために追加
#include <string.h>
#include <stdlib.h>
#include <raylib.h>
#include <stdio.h>
#include <iconv.h>
#include <errno.h>

// グローバルインスタンス
LyricsDisplay gLyricsDisplay;

// Shift-JISからUTF-8への変換関数
static char* convertShiftJisToUtf8(const char* input) {
    if (!input || input[0] == '\0') return strdup("");

    iconv_t cd = iconv_open("UTF-8", "SHIFT-JIS");
    if (cd == (iconv_t)-1) {
        fprintf(stderr, "iconv_open failed: %s\n", strerror(errno));
        return strdup(input);  // 変換できない場合は元の文字列を返す
    }

    size_t inbytesleft = strlen(input);
    // UTF-8は最大でShift-JISの3倍のサイズになる可能性がある
    size_t outbytesleft = inbytesleft * 3 + 1;
    char* outbuf = (char*)malloc(outbytesleft);
    if (!outbuf) {
        iconv_close(cd);
        return strdup(input);
    }

    char* inbuf = (char*)input;
    char* outbufptr = outbuf;

    if (iconv(cd, &inbuf, &inbytesleft, &outbufptr, &outbytesleft) == (size_t)-1) {
        fprintf(stderr, "iconv failed: %s\n", strerror(errno));
        free(outbuf);
        iconv_close(cd);
        return strdup(input);
    }

    *outbufptr = '\0';  // NULL終端
    iconv_close(cd);

    return outbuf;
}

// エンコーディングを自動検出する関数
static bool isLikelyShiftJis(const char* text) {
    // Shift-JISの特徴的なバイトパターンをチェック
    unsigned char *bytes = (unsigned char*)text;
    int i = 0;

    while (bytes[i]) {
        // 1バイト目が0x81-0x9F または 0xE0-0xEF の範囲
        if ((bytes[i] >= 0x81 && bytes[i] <= 0x9F) || (bytes[i] >= 0xE0 && bytes[i] <= 0xEF)) {
            // 2バイト目が0x40-0xFC の範囲
            if (bytes[i+1] >= 0x40 && bytes[i+1] <= 0xFC && bytes[i+1] != 0x7F) {
                return true;
            }
        }
        i++;
    }

    return false;
}

// 初期化
void initLyricsDisplay() {
    // 既に初期化されている場合は何もしない
    if (gLyricsDisplay.initialized) return;

    // データ構造をクリア
    memset(&gLyricsDisplay, 0, sizeof(LyricsDisplay));

    // ミューテックスの初期化
    pthread_mutex_init(&gLyricsDisplay.mutex, NULL);

    // デフォルト設定
    gLyricsDisplay.showLyrics = true;
    gLyricsDisplay.showMarkers = true;
    gLyricsDisplay.fadeTime = 2000;  // 2秒のフェード時間
    gLyricsDisplay.fontSize = 24;    // デフォルトフォントサイズ

    // 初期化完了
    gLyricsDisplay.initialized = true;
}

// クリーンアップ
void cleanupLyricsDisplay() {
    if (!gLyricsDisplay.initialized) return;

    pthread_mutex_destroy(&gLyricsDisplay.mutex);
    gLyricsDisplay.initialized = false;
}

// 歌詞イベントを追加（エンコーディング対応版）
void addLyricEvent(uint64_t tick, const char* text) {
    if (!gLyricsDisplay.initialized) return;

    // Shift-JIS検出と変換
    char* convertedText = NULL;
    if (isLikelyShiftJis(text)) {
        convertedText = convertShiftJisToUtf8(text);
    } else {
        convertedText = strdup(text);
    }

    if (!convertedText) {
        fprintf(stderr, "Memory allocation failed for text conversion\n");
        return;
    }

    pthread_mutex_lock(&gLyricsDisplay.mutex);

    // 最大数を超えていないか確認
    if (gLyricsDisplay.lyricsCount < MAX_LYRICS_EVENTS) {
        // 同じティック位置にすでにイベントがあるか確認
        for (int i = 0; i < gLyricsDisplay.lyricsCount; i++) {
            if (gLyricsDisplay.lyrics[i].tick == tick) {
                // 同じティック位置の場合、テキストを連結
                int remaining = MAX_EVENT_TEXT_LENGTH - strlen(gLyricsDisplay.lyrics[i].text) - 1;
                if (remaining > 0) {
                    strncat(gLyricsDisplay.lyrics[i].text, " ", remaining);
                    remaining--;
                }
                if (remaining > 0) {
                    strncat(gLyricsDisplay.lyrics[i].text, convertedText, remaining);
                }
                pthread_mutex_unlock(&gLyricsDisplay.mutex);
                free(convertedText);
                return;
            }
        }

        // 新しいイベントを追加
        int index = gLyricsDisplay.lyricsCount++;
        gLyricsDisplay.lyrics[index].tick = tick;
        strncpy(gLyricsDisplay.lyrics[index].text, convertedText, MAX_EVENT_TEXT_LENGTH - 1);
        gLyricsDisplay.lyrics[index].text[MAX_EVENT_TEXT_LENGTH - 1] = '\0';
        gLyricsDisplay.lyrics[index].displayed = false;

        // ティック順にソート（単純な挿入ソート）
        for (int i = index; i > 0; i--) {
            if (gLyricsDisplay.lyrics[i].tick < gLyricsDisplay.lyrics[i-1].tick) {
                TextEvent temp = gLyricsDisplay.lyrics[i];
                gLyricsDisplay.lyrics[i] = gLyricsDisplay.lyrics[i-1];
                gLyricsDisplay.lyrics[i-1] = temp;
            } else {
                break;
            }
        }
    }

    pthread_mutex_unlock(&gLyricsDisplay.mutex);
    free(convertedText);
}

// マーカーイベントを追加（エンコーディング対応版）
void addMarkerEvent(uint64_t tick, const char* text) {
    if (!gLyricsDisplay.initialized) return;

    // Shift-JIS検出と変換
    char* convertedText = NULL;
    if (isLikelyShiftJis(text)) {
        convertedText = convertShiftJisToUtf8(text);
    } else {
        convertedText = strdup(text);
    }

    if (!convertedText) {
        fprintf(stderr, "Memory allocation failed for text conversion\n");
        return;
    }

    pthread_mutex_lock(&gLyricsDisplay.mutex);

    // 最大数を超えていないか確認
    if (gLyricsDisplay.markerCount < MAX_MARKER_EVENTS) {
        // 同じティック位置にすでにイベントがあるか確認
        for (int i = 0; i < gLyricsDisplay.markerCount; i++) {
            if (gLyricsDisplay.markers[i].tick == tick) {
                // 同じティック位置の場合、テキストを上書き
                strncpy(gLyricsDisplay.markers[i].text, convertedText, MAX_EVENT_TEXT_LENGTH - 1);
                gLyricsDisplay.markers[i].text[MAX_EVENT_TEXT_LENGTH - 1] = '\0';
                pthread_mutex_unlock(&gLyricsDisplay.mutex);
                free(convertedText);
                return;
            }
        }

        // 新しいイベントを追加
        int index = gLyricsDisplay.markerCount++;
        gLyricsDisplay.markers[index].tick = tick;
        strncpy(gLyricsDisplay.markers[index].text, convertedText, MAX_EVENT_TEXT_LENGTH - 1);
        gLyricsDisplay.markers[index].text[MAX_EVENT_TEXT_LENGTH - 1] = '\0';
        gLyricsDisplay.markers[index].displayed = false;

        // ティック順にソート（単純な挿入ソート）
        for (int i = index; i > 0; i--) {
            if (gLyricsDisplay.markers[i].tick < gLyricsDisplay.markers[i-1].tick) {
                TextEvent temp = gLyricsDisplay.markers[i];
                gLyricsDisplay.markers[i] = gLyricsDisplay.markers[i-1];
                gLyricsDisplay.markers[i-1] = temp;
            } else {
                break;
            }
        }
    }

    pthread_mutex_unlock(&gLyricsDisplay.mutex);
    free(convertedText);
}

// テキストが空白のみかどうかを確認する関数
static bool isWhitespaceOnly(const char* text) {
    if (!text || text[0] == '\0') return true;

    const unsigned char* p = (const unsigned char*)text;
    while (*p) {
        // 全角スペース (U+3000) のUTF-8表現: E3 80 80
        if (*p == 0xE3 && *(p+1) == 0x80 && *(p+2) == 0x80) {
            p += 3;
            continue;
        }
        // 通常の半角スペースとその他の空白文字
        if (*p != ' ' && *p != '\t' && *p != '\n' && *p != '\r') {
            // NULL文字チェックを追加して境界外アクセスを防止
            if (*(p+1) == '\0' || *(p+2) == '\0') {
                return false;
            }
            return false;
        }
        p++;
    }
    return true;
}

// 現在のティック位置に基づいて表示するテキストを更新
void updateLyricsDisplay(uint64_t currentTick) {
    if (!gLyricsDisplay.initialized) return;

    pthread_mutex_lock(&gLyricsDisplay.mutex);

    // 一つ前のティック位置のために少しマージンを持たせる
    uint64_t lookAheadTick = currentTick + (gMidiFile && gMidiFile->Division ? gMidiFile->Division / 8 : 10);

    // 歌詞イベントの更新
    for (int i = 0; i < gLyricsDisplay.lyricsCount; i++) {
        if (gLyricsDisplay.lyrics[i].tick <= lookAheadTick && !gLyricsDisplay.lyrics[i].displayed) {
            // 新しい歌詞イベントを見つけた
            if (isWhitespaceOnly(gLyricsDisplay.lyrics[i].text)) {
                // 空または空白のみの場合は現在の歌詞をクリア
                gLyricsDisplay.currentLyric[0] = '\0';
            } else {
                strncpy(gLyricsDisplay.currentLyric, gLyricsDisplay.lyrics[i].text, MAX_EVENT_TEXT_LENGTH - 1);
                gLyricsDisplay.currentLyric[MAX_EVENT_TEXT_LENGTH - 1] = '\0';
            }
            gLyricsDisplay.lyrics[i].displayed = true;
        }
    }

    // マーカーイベントの更新
    for (int i = 0; i < gLyricsDisplay.markerCount; i++) {
        if (gLyricsDisplay.markers[i].tick <= lookAheadTick && !gLyricsDisplay.markers[i].displayed) {
            // 新しいマーカーイベントを見つけた
            if (isWhitespaceOnly(gLyricsDisplay.markers[i].text)) {
                // 空または空白のみの場合は現在のマーカーをクリア
                gLyricsDisplay.currentMarker[0] = '\0';
            } else {
                strncpy(gLyricsDisplay.currentMarker, gLyricsDisplay.markers[i].text, MAX_EVENT_TEXT_LENGTH - 1);
                gLyricsDisplay.currentMarker[MAX_EVENT_TEXT_LENGTH - 1] = '\0';
            }
            gLyricsDisplay.markers[i].displayed = true;
        }
    }

    pthread_mutex_unlock(&gLyricsDisplay.mutex);
}

// 歌詞とマーカーを画面に描画
void drawLyricsDisplay() {
    if (!gLyricsDisplay.initialized) return;

    Font font = getJapaneseFont(); // GUIモジュールからフォントを取得

    // 歌詞を描画
    if (gLyricsDisplay.showLyrics && strlen(gLyricsDisplay.currentLyric) > 0) {
        int fontSize = gLyricsDisplay.fontSize;
        // 左側に表示する位置を設定（左端から20ピクセルの余白）
        int x = 10;
        int y = 220 - fontSize;

        // 歌詞テキストを描画
        DrawTextEx(font, gLyricsDisplay.currentLyric, (Vector2){ x, y }, fontSize, 1.0f, WHITE);
    }

    // マーカーを描画
    if (gLyricsDisplay.showMarkers && strlen(gLyricsDisplay.currentMarker) > 0) {
        int fontSize = gLyricsDisplay.fontSize - 2;
        // 左側に表示する位置を設定（左端から20ピクセルの余白）
        int x = 10;
        int y = 200 - fontSize;

        // マーカーテキストを描画
        DrawTextEx(font, gLyricsDisplay.currentMarker, (Vector2){ x, y }, fontSize, 1.0f, YELLOW);
    }
}

// フォントサイズ変更関数
void changeLyricsFontSize(int size) {
    if (!gLyricsDisplay.initialized) return;

    // サイズの範囲を制限（最小12、最大48）
    if (size < 12) size = 12;
    if (size > 48) size = 48;

    pthread_mutex_lock(&gLyricsDisplay.mutex);
    gLyricsDisplay.fontSize = size;
    pthread_mutex_unlock(&gLyricsDisplay.mutex);
}

// 歌詞表示の切り替え
void toggleLyricsDisplay() {
    if (!gLyricsDisplay.initialized) return;

    pthread_mutex_lock(&gLyricsDisplay.mutex);
    gLyricsDisplay.showLyrics = !gLyricsDisplay.showLyrics;
    pthread_mutex_unlock(&gLyricsDisplay.mutex);
}

// マーカー表示の切り替え
void toggleMarkersDisplay() {
    if (!gLyricsDisplay.initialized) return;

    pthread_mutex_lock(&gLyricsDisplay.mutex);
    gLyricsDisplay.showMarkers = !gLyricsDisplay.showMarkers;
    pthread_mutex_unlock(&gLyricsDisplay.mutex);
}

// イベントをリセット（新しいMIDIファイル読み込み時）
void resetLyricsEvents() {
    if (!gLyricsDisplay.initialized) return;

    pthread_mutex_lock(&gLyricsDisplay.mutex);

    // イベントカウントをリセット
    gLyricsDisplay.lyricsCount = 0;
    gLyricsDisplay.markerCount = 0;

    // 現在表示中のテキストをクリア
    gLyricsDisplay.currentLyric[0] = '\0';
    gLyricsDisplay.currentMarker[0] = '\0';

    pthread_mutex_unlock(&gLyricsDisplay.mutex);
}
