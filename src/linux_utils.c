#include "linux_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/wait.h>
#include <sys/stat.h>
#include <errno.h>

#ifdef __linux__

// Linux固有のファイルダイアログを開く
char* linux_open_file_dialog(const char* title, const char* filter) {
    char* result = NULL;
    char command[1024];
    FILE* fp;
    char path[1024];
    
    // zenityが利用可能かチェック
    if (system("which zenity > /dev/null 2>&1") == 0) {
        snprintf(command, sizeof(command), 
            "zenity --file-selection --title=\"%s\" --file-filter=\"%s\"",
            title ? title : "ファイルを選択", 
            filter ? filter : "MIDI files (*.mid *.midi) | *.mid *.midi");
    }
    // kdialogが利用可能かチェック
    else if (system("which kdialog > /dev/null 2>&1") == 0) {
        snprintf(command, sizeof(command),
            "kdialog --getopenfilename . \"%s\"",
            filter ? filter : "*.mid *.midi|MIDI files");
    }
    // xdg-openが利用可能かチェック（フォールバック）
    else if (system("which xdg-open > /dev/null 2>&1") == 0) {
        // xdg-openはファイル選択ダイアログを直接提供しないので、
        // ファイルマネージャーを開く
        system("xdg-open .");
        return NULL;
    }
    else {
        fprintf(stderr, "ファイルダイアログが利用できません。zenity または kdialog をインストールしてください。\n");
        return NULL;
    }
    
    fp = popen(command, "r");
    if (fp == NULL) {
        fprintf(stderr, "ファイルダイアログの実行に失敗しました: %s\n", strerror(errno));
        return NULL;
    }
    
    if (fgets(path, sizeof(path), fp) != NULL) {
        // 改行文字を削除
        size_t len = strlen(path);
        if (len > 0 && path[len-1] == '\n') {
            path[len-1] = '\0';
        }
        
        // パスが空でない場合のみ結果を返す
        if (strlen(path) > 0) {
            result = malloc(strlen(path) + 1);
            if (result) {
                strcpy(result, path);
            }
        }
    }
    
    pclose(fp);
    return result;
}

// Linux固有のシステム通知を送信
void linux_send_notification(const char* title, const char* message, const char* icon) {
    char command[1024];
    
    // notify-sendが利用可能かチェック
    if (system("which notify-send > /dev/null 2>&1") == 0) {
        snprintf(command, sizeof(command),
            "notify-send \"%s\" \"%s\" %s%s%s",
            title ? title : "HMP",
            message ? message : "",
            icon ? "--icon=" : "",
            icon ? icon : "",
            icon ? "" : "");
        system(command);
    }
}

// Linux固有のオーディオシステム検出
AudioSystem linux_detect_audio_system() {
    // PipeWireの検出
    if (system("pgrep -x pipewire > /dev/null 2>&1") == 0) {
        return AUDIO_PIPEWIRE;
    }
    
    // PulseAudioの検出
    if (system("pgrep -x pulseaudio > /dev/null 2>&1") == 0) {
        return AUDIO_PULSEAUDIO;
    }
    
    // ALSAの検出（/proc/asound/の存在確認）
    struct stat st;
    if (stat("/proc/asound", &st) == 0 && S_ISDIR(st.st_mode)) {
        return AUDIO_ALSA;
    }
    
    return AUDIO_UNKNOWN;
}

// Linux固有のオーディオ設定を取得
const char* linux_get_audio_system_name(AudioSystem system) {
    switch (system) {
        case AUDIO_PIPEWIRE:
            return "PipeWire";
        case AUDIO_PULSEAUDIO:
            return "PulseAudio";
        case AUDIO_ALSA:
            return "ALSA";
        default:
            return "Unknown";
    }
}

// Linux固有のディスプレイサーバー検出
DisplayServer linux_detect_display_server() {
    // Waylandの検出
    if (getenv("WAYLAND_DISPLAY") != NULL) {
        return DISPLAY_WAYLAND;
    }
    
    // X11の検出
    if (getenv("DISPLAY") != NULL) {
        return DISPLAY_X11;
    }
    
    return DISPLAY_UNKNOWN;
}

// Linux固有のディスプレイサーバー名を取得
const char* linux_get_display_server_name(DisplayServer server) {
    switch (server) {
        case DISPLAY_WAYLAND:
            return "Wayland";
        case DISPLAY_X11:
            return "X11";
        default:
            return "Unknown";
    }
}

// Linux固有のシステム情報を取得
void linux_get_system_info(LinuxSystemInfo* info) {
    if (!info) return;
    
    // 初期化
    memset(info, 0, sizeof(LinuxSystemInfo));
    
    // オーディオシステム検出
    info->audio_system = linux_detect_audio_system();
    
    // ディスプレイサーバー検出
    info->display_server = linux_detect_display_server();
    
    // デスクトップ環境の検出
    const char* desktop = getenv("XDG_CURRENT_DESKTOP");
    if (desktop) {
        strncpy(info->desktop_environment, desktop, sizeof(info->desktop_environment) - 1);
    } else {
        strcpy(info->desktop_environment, "Unknown");
    }
    
    // ディストリビューション情報の取得
    FILE* fp = fopen("/etc/os-release", "r");
    if (fp) {
        char line[256];
        while (fgets(line, sizeof(line), fp)) {
            if (strncmp(line, "PRETTY_NAME=", 12) == 0) {
                // クォートを削除
                char* start = strchr(line, '"');
                if (start) {
                    start++;
                    char* end = strrchr(start, '"');
                    if (end) {
                        *end = '\0';
                        strncpy(info->distribution, start, sizeof(info->distribution) - 1);
                    }
                }
                break;
            }
        }
        fclose(fp);
    }
    
    if (strlen(info->distribution) == 0) {
        strcpy(info->distribution, "Unknown Linux");
    }
}

// Linux固有のシステム情報を表示
void linux_print_system_info() {
    LinuxSystemInfo info;
    linux_get_system_info(&info);
    
    printf("=== Linux System Information ===\n");
    printf("Distribution: %s\n", info.distribution);
    printf("Desktop Environment: %s\n", info.desktop_environment);
    printf("Display Server: %s\n", linux_get_display_server_name(info.display_server));
    printf("Audio System: %s\n", linux_get_audio_system_name(info.audio_system));
    printf("================================\n");
}

#endif // __linux__
