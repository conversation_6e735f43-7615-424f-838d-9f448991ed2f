#include "setting_window.h"
#include <stdio.h>
#include "raygui_impl.h"
#include "lyrics_display.h"  // 歌詞表示モジュールを追加
#include "camera.h"          // camera.hをインクルード（88鍵盤モード用）
#include "keyboard_display.h" // キーボード2D表示モジュールを追加
#include "gui.h"             // 日本語フォント用のヘッダーを追加

// 設定ウィンドウの表示状態
bool showSettingWindow = false;

// ピッチベンド機能の有効/無効設定（デフォルトは有効）
bool enablePitchBend = true;

// キーボード2D表示の有効/無効設定（デフォルトは無効）
bool enableKeyboardDisplay = false;

// Keyboard shortcut settings (all enabled by default)
bool enableLyricsToggle = true;       // F4: Toggle lyrics display
bool enableMarkersToggle = true;      // F5: Toggle markers display

// Setting window tab state
typedef enum { TAB_GENERAL, TAB_SHORTCUTS } SettingTab;
static SettingTab currentTab = TAB_GENERAL;

// Reference to channelPitchBends array
extern int channelPitchBends[16];

// Toggle visibility of the settings window
void toggleSettingWindow(void) {
    showSettingWindow = !showSettingWindow;
}

// Function to update visual effects immediately after settings changes
void updateAllPitchBendEffects() {
    // Update pitch bend values for all channels
    for (int ch = 0; ch < 16; ch++) {
        // Unused variable removed
        // int currentValue = channelPitchBends[ch];
    }
}

// Function to draw setting tabs
void drawSettingTabs(int windowX, int windowY, int windowWidth, int *contentY) {
    // Tab button size and position
    int tabHeight = 30;
    int tabWidth = windowWidth / 2;

    Rectangle generalTabRect = { windowX, windowY + 30, tabWidth, tabHeight };
    Rectangle shortcutsTabRect = { windowX + tabWidth, windowY + 30, tabWidth, tabHeight };

    // Change style for selected tab
    if (GuiButton(generalTabRect, "General")) {
        currentTab = TAB_GENERAL;
    }

    if (GuiButton(shortcutsTabRect, "Keyboard Shortcuts")) {
        currentTab = TAB_SHORTCUTS;
    }

    // Highlight selected tab
    if (currentTab == TAB_GENERAL) {
        DrawRectangleLines(generalTabRect.x, generalTabRect.y, generalTabRect.width, generalTabRect.height, BLUE);
    } else {
        DrawRectangleLines(shortcutsTabRect.x, shortcutsTabRect.y, shortcutsTabRect.width, shortcutsTabRect.height, BLUE);
    }

    // Draw text with larger font size (changed from Japanese to English)
    Font japaneseFont = getJapaneseFont();
    int fontSize = 20; // Increased from 16 to 20

    Vector2 generalTabTextPos = { generalTabRect.x + generalTabRect.width/2 - MeasureTextEx(japaneseFont, "General", fontSize, 1).x/2, generalTabRect.y + 5 };
    Vector2 shortcutsTabTextPos = { shortcutsTabRect.x + shortcutsTabRect.width/2 - MeasureTextEx(japaneseFont, "Shortcuts", fontSize, 1).x/2, shortcutsTabRect.y + 5 };

    // Clear default button text (overwrite with white rectangle)
    DrawRectangle(generalTabRect.x + 5, generalTabRect.y + 5, generalTabRect.width - 10, 20, (Color){245, 245, 245, 255});
    DrawRectangle(shortcutsTabRect.x + 5, shortcutsTabRect.y + 5, shortcutsTabRect.width - 10, 20, (Color){245, 245, 245, 255});

    // Draw English text with larger font
    DrawTextEx(japaneseFont, "General", generalTabTextPos, fontSize, 1, BLACK);
    DrawTextEx(japaneseFont, "Shortcuts", shortcutsTabTextPos, fontSize, 1, BLACK);

    *contentY = windowY + 70; // Starting Y position for content below tabs
}

// Draw general settings tab content
void drawGeneralTab(int windowX, int windowY, int windowWidth, int contentY) {
    // Explicitly handle unused parameters (suppress warnings)
    (void)windowY;
    (void)windowWidth;

    int labelWidth = 200;
    Font japaneseFont = getJapaneseFont();
    int fontSize = 20; // Increased from 16 to 20

    // Global pitch bend enable/disable setting
    bool prevGlobalState = enablePitchBend;
    if (GuiCheckBox(
        (Rectangle){windowX + 20, contentY, 20, 20},
        "Enable Pitch Bend Effect", &enablePitchBend)) {
        // Process only if state has changed
        if (prevGlobalState != enablePitchBend) {
            // Update visual effects immediately
            updateAllPitchBendEffects();
        }
    }

    // Clear standard text and draw larger English text
    DrawRectangle(windowX + 45, contentY, 200, 20, (Color){245, 245, 245, 255});
    DrawTextEx(japaneseFont, "Enable Pitch Bend Effect", (Vector2){windowX + 45, contentY}, fontSize, 1, BLACK);

    contentY += 35; // Increased spacing for larger font

    // 2D keyboard display enable/disable setting
    bool prevKeyboardDisplayState = enableKeyboardDisplay;
    if (GuiCheckBox(
        (Rectangle){windowX + 20, contentY, 20, 20},
        "Show 2D Keyboard Display", &enableKeyboardDisplay)) {
        // Process only if state has changed
        if (prevKeyboardDisplayState != enableKeyboardDisplay) {
            // Update keyboard display state to ensure reflection
            showKeyboardDisplay = enableKeyboardDisplay;
            fprintf(stderr, "Keyboard display toggled: %s\n", enableKeyboardDisplay ? "ON" : "OFF");
        }
    }

    // Clear standard text and draw larger English text
    DrawRectangle(windowX + 45, contentY, 200, 20, (Color){245, 245, 245, 255});
    DrawTextEx(japaneseFont, "Show 2D Keyboard Display", (Vector2){windowX + 45, contentY}, fontSize, 1, BLACK);

    contentY += 35; // Increased spacing

    // Display toggle section
    DrawRectangle(windowX + 20, contentY, labelWidth, 25, (Color){245, 245, 245, 255});
    DrawTextEx(japaneseFont, "Display Settings:", (Vector2){windowX + 20, contentY}, fontSize, 1, BLACK);
    contentY += 30; // Increased spacing

    // Lyrics display toggle button
    bool prevLyricsState = gLyricsDisplay.showLyrics;
    if (GuiCheckBox((Rectangle){windowX + 20, contentY, 20, 20}, "Show Lyrics Display", &gLyricsDisplay.showLyrics)) {
        if (prevLyricsState != gLyricsDisplay.showLyrics) {
            // Currently just changing variable directly, no additional processing needed
        }
    }

    // Clear standard text and draw larger English text
    DrawRectangle(windowX + 45, contentY, 200, 20, (Color){245, 245, 245, 255});
    DrawTextEx(japaneseFont, "Show Lyrics Display", (Vector2){windowX + 45, contentY}, fontSize, 1, BLACK);

    contentY += 35; // Increased spacing

    // Markers display toggle button
    bool prevMarkersState = gLyricsDisplay.showMarkers;
    if (GuiCheckBox((Rectangle){windowX + 20, contentY, 20, 20}, "Show Markers Display", &gLyricsDisplay.showMarkers)) {
        if (prevMarkersState != gLyricsDisplay.showMarkers) {
            // Currently just changing variable directly, no additional processing needed
        }
    }

    // Clear standard text and draw larger English text
    DrawRectangle(windowX + 45, contentY, 200, 20, (Color){245, 245, 245, 255});
    DrawTextEx(japaneseFont, "Show Markers Display", (Vector2){windowX + 45, contentY}, fontSize, 1, BLACK);
}

// Draw keyboard shortcuts tab content
void drawShortcutsTab(int windowX, int windowY, int windowWidth, int contentY) {
    // Explicitly handle unused parameters (suppress warnings)
    (void)windowY;

    int padding = 20;
    int fontSize = 16; // Increased from 12 to 16
    int lineHeight = 30; // Increased from 24 to 30
    int column1Width = 80;
    Font japaneseFont = getJapaneseFont();

    // Keep scroll state in static variable
    static Vector2 scrollOffset = { 0.0f, 0.0f };

    // Display shortcuts list (translated to English)
    const char* shortcuts[][2] = {
        {"F1", "Show/Hide Settings Window"},
        {"F2", "Switch to 128-key Mode"},
        {"F3", "Switch to 88-key Mode"},
        {"F4", "Toggle Adaptive View Mode"},
        {"Space", "Play/Pause MIDI"},
        {"R", "Restart Playback"},
        {"L", "Toggle Relative Time Mode"},
        {"+/-", "Adjust BPM by 10%"},
        {"[/]", "Adjust BPM by 5%"},
        {"0", "Reset to Normal Speed"},
        {"←/→", "Rewind/Fast Forward"},
        {"Home", "Jump to Start of Song"},
        {"End", "Jump to End of Song"},
        {"V", "Toggle VSync"},
        {"H", "Show/Hide UI Elements"}
    };

    int shortcutCount = sizeof(shortcuts) / sizeof(shortcuts[0]);

    // Calculate actual content height (header + items × line height + padding)
    float contentHeight = 40 + shortcutCount * lineHeight + 20;

    // Scroll panel settings
    Rectangle panelRect = { windowX + 10, contentY, windowWidth - 20, 280 }; // Size that fits in window
    Rectangle contentRect = { 0, 0, panelRect.width - 20, contentHeight }; // Actual content size
    Rectangle viewRect = { 0 }; // Variable to receive view area

    // Display scroll panel
    GuiScrollPanel(panelRect, NULL, contentRect, &scrollOffset, &viewRect);

    // Set scissor mode to draw within scroll panel
    BeginScissorMode(viewRect.x, viewRect.y, viewRect.width, viewRect.height);

    // Header (drawn at position based on scroll offset, not fixed position)
    DrawTextEx(japaneseFont, "Key", (Vector2){viewRect.x + padding, viewRect.y + 10 + scrollOffset.y}, fontSize + 2, 1, DARKBLUE);
    DrawTextEx(japaneseFont, "Function", (Vector2){viewRect.x + padding + column1Width, viewRect.y + 10 + scrollOffset.y}, fontSize + 2, 1, DARKBLUE);

    // Starting Y position for list items
    int itemY = viewRect.y + 40 + scrollOffset.y;

    // Display all shortcuts
    for (int i = 0; i < shortcutCount; i++) {
        DrawTextEx(japaneseFont, shortcuts[i][0], (Vector2){viewRect.x + padding, itemY}, fontSize, 1, MAROON);
        DrawTextEx(japaneseFont, shortcuts[i][1], (Vector2){viewRect.x + padding + column1Width, itemY}, fontSize, 1, DARKGRAY);
        itemY += lineHeight;
    }

    // End scissor mode
    EndScissorMode();

    // Redraw scrollbar only (optional)
    DrawRectangleLines(panelRect.x + panelRect.width - 12, panelRect.y, 12, panelRect.height, LIGHTGRAY);
}

// Draw settings window
void drawSettingWindow(void) {
    // Don't draw if window is not in display state
    if (!showSettingWindow) return;

    // Add feature to close window with ESC key
    if (IsKeyPressed(KEY_ESCAPE)) {
        showSettingWindow = false;
        return;
    }

    // Window size (expanded to be larger)
    int windowWidth = 400; // Increased from 350 to 400
    int windowHeight = 450; // Increased from 400 to 450

    // Window position
    int screenWidth = GetScreenWidth();
    int screenHeight = GetScreenHeight();
    int windowX = screenWidth / 2 - windowWidth / 2;
    int windowY = screenHeight / 2 - windowHeight / 2;

    // Semi-transparent overlay (modal display)
    DrawRectangle(0, 0, screenWidth, screenHeight, (Color){0, 0, 0, 100});

    // Draw window
    Rectangle windowRect = {windowX, windowY, windowWidth, windowHeight};
    if (GuiWindowBox(windowRect, "Settings")) {
        // If window close button is pressed
        showSettingWindow = false;
    }

    // Draw tabs
    int contentY;
    drawSettingTabs(windowX, windowY, windowWidth, &contentY);

    // Draw content according to current tab
    if (currentTab == TAB_GENERAL) {
        drawGeneralTab(windowX, windowY, windowWidth, contentY);
    } else {
        drawShortcutsTab(windowX, windowY, windowWidth, contentY);
    }

    // Button area
    int buttonY = windowY + windowHeight - 40;
    if (GuiButton((Rectangle){windowX + windowWidth - 90, buttonY, 80, 30}, "Close")) {
        showSettingWindow = false;
    }

    // Draw English text for close button with larger font
    Font japaneseFont = getJapaneseFont();
    int fontSize = 20; // Increased from 16 to 20
    DrawRectangle(windowX + windowWidth - 90 + 5, buttonY + 5, 70, 20, (Color){245, 245, 245, 255});
    Vector2 closeTextPos = {windowX + windowWidth - 90 + 40 - MeasureTextEx(japaneseFont, "Close", fontSize, 1).x/2, buttonY + 5};
    DrawTextEx(japaneseFont, "Close", closeTextPos, fontSize, 1, BLACK);
}
