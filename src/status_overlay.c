#define WIN32_LEAN_AND_MEAN
#define NOMINMAX
#define Rectangle RaylibRectangle
#define CloseWindow RaylibCloseWindow
#define DrawText RaylibDrawText
#define ShowCursor RaylibShowCursor
#include <raylib.h>
#undef Rectangle
#undef CloseWindow
#undef DrawText
#undef ShowCursor

#include "status_overlay.h"
#include "midiplayer.h"  // Using relative path
#include "camera.h"      // Using relative path
#include <stdio.h>

// External variable declarations for camera-related settings
extern bool isAdaptiveViewEnabled;
extern bool isTransitioning;
extern bool is88KeyMode;
extern bool targetIs128KeyMode;
extern float cameraTransitionProgress;

// External function declarations (from midi_visualizer.c)
extern int getCurrentPolyphony(void);
extern int getTotalNotes(void);
extern int getNotesCount(void);
extern int getCurrentNotes(void);
extern float getNotesPerSecond(void);

// Global variable for BPM information
static struct {
    float minBpm;
    float maxBpm;
    float avgBpm;
    bool initialized;
} bpmInfo = {
    .minBpm = 120.0f,
    .maxBpm = 120.0f,
    .avgBpm = 120.0f,
    .initialized = false
};

// Global variable for note statistics
static struct {
    int currentNotes;
    int totalNotes;
    int notesCount;
    float notesPerSecond;
    int polyphony;
    unsigned int lastUpdateTime;
    // Variables for smooth display
    float smoothNPS;          // Smoothed NPS value
    int smoothPolyphony;      // Smoothed polyphony value
    int smoothCurrentNotes;   // Smoothed current note count
    float maxNPS;             // Add max NPS tracking
    int maxPolyphony;         // Add max polyphony tracking
} noteStats = {
    .currentNotes = 0,
    .totalNotes = 0,
    .notesCount = 0,
    .notesPerSecond = 0.0f,
    .polyphony = 0,
    .lastUpdateTime = 0,
    .smoothNPS = 0.0f,
    .smoothPolyphony = 0,
    .smoothCurrentNotes = 0,
    .maxNPS = 0.0f,
    .maxPolyphony = 0
};

// Function to initialize/update BPM information
void updateBpmInfo() {
    if (!isMidiLoaded()) {
        bpmInfo.minBpm = 120.0f;
        bpmInfo.maxBpm = 120.0f;
        bpmInfo.avgBpm = 120.0f;
        bpmInfo.initialized = false;
        return;
    }

    // Get BPM information from MIDI file
    getMidiBpmInfo(&bpmInfo.minBpm, &bpmInfo.maxBpm, &bpmInfo.avgBpm);
    bpmInfo.initialized = true;

    // Debug output
    printf("Updated BPM info: Min=%.1f, Max=%.1f, Avg=%.1f\n",
           bpmInfo.minBpm, bpmInfo.maxBpm, bpmInfo.avgBpm);
}

// Function to update note statistics
void updateNoteStats() {
    if (!isMidiLoaded()) {
        noteStats.currentNotes = 0;
        noteStats.totalNotes = 0;
        noteStats.notesCount = 0;
        noteStats.notesPerSecond = 0.0f;
        noteStats.polyphony = 0;
        noteStats.smoothNPS = 0.0f;
        noteStats.smoothPolyphony = 0;
        noteStats.smoothCurrentNotes = 0;
        noteStats.maxNPS = 0.0f;
        noteStats.maxPolyphony = 0;
        return;
    }

    unsigned int currentTime = GetTime() * 1000;

    // Update at fixed intervals (33ms ≈ 30FPS)
    if (currentTime - noteStats.lastUpdateTime > 33) {
        // Get actual values
        int currentPolyphony = getCurrentPolyphony();
        int currentActiveNotes = getCurrentNotes();
        int currentTotalNotes = getTotalNotes();
        int currentNotesCount = getNotesCount();
        float currentNPS = getNotesPerSecond();

        // Smoothing factor (0.0-1.0) - lower values make smoother transitions
        const float smoothFactor = 0.8f;

        // Calculate smoothed values (linear interpolation)
        noteStats.smoothNPS = noteStats.smoothNPS + (currentNPS - noteStats.smoothNPS) * smoothFactor;
        noteStats.smoothPolyphony = (int)(noteStats.smoothPolyphony + (currentPolyphony - noteStats.smoothPolyphony) * smoothFactor);
        noteStats.smoothCurrentNotes = (int)(noteStats.smoothCurrentNotes + (currentActiveNotes - noteStats.smoothCurrentNotes) * smoothFactor);

        // Update max values
        if (currentNPS > noteStats.maxNPS) {
            noteStats.maxNPS = currentNPS;
        }
        if (currentPolyphony > noteStats.maxPolyphony) {
            noteStats.maxPolyphony = currentPolyphony;
        }

        // Update stored values
        noteStats.currentNotes = currentActiveNotes;
        noteStats.totalNotes = currentTotalNotes;
        noteStats.notesCount = currentNotesCount;
        noteStats.notesPerSecond = currentNPS;
        noteStats.polyphony = currentPolyphony;
        noteStats.lastUpdateTime = currentTime;
    }
}

// Function to display the status overlay (time and BPM information)
void drawStatusOverlay() {
    // Don't display if no MIDI file is loaded
    if (!isMidiLoaded()) return;

    // Position in top-right corner
    int screenWidth = GetScreenWidth();
    int margin = -5;
    int timeY = 26;  // Position below progress bar

    // Get current time and total time
    char currentTime[16] = "00:00";
    char totalTime[16] = "00:00";
    getMidiTimeString(currentTime, sizeof(currentTime), false);
    getMidiTimeString(totalTime, sizeof(totalTime), true);

    // Get BPM-related information
    float currentBPM = getCurrentBPM();
    float multiplier = getBpmMultiplier();

    // Update BPM information if not initialized
    if (!bpmInfo.initialized) {
        updateBpmInfo();
    }

    // Update note statistics
    updateNoteStats();

    // 表示項目を7つから8つに増やす（Renderedを追加）
    const char *labels[] = {"Time:", "BPM:", "View:", "FPS:", "Notes:", "NPS (Max):", "Poly (Max):", "Rendered:"};
    char values[8][128];  // 配列サイズを8に増やす

    // Create value strings
    sprintf(values[0], "%s / %s", currentTime, totalTime);
    sprintf(values[1], "%.0f (%.2fx)", currentBPM, multiplier);

    // Extend adaptive mode display (more detailed current state)
    if (isAdaptiveViewEnabled) {
        if (isTransitioning) {
            // Show progress during transition
            sprintf(values[2], "Adaptive [%s->%s %.0f%%]",
                    is88KeyMode ? "88" : "128",
                    targetIs128KeyMode ? "128" : "88",
                    targetIs128KeyMode ? cameraTransitionProgress * 100.0f :
                                        (1.0f - cameraTransitionProgress) * 100.0f);
        } else {
            // Show current mode during normal operation
            sprintf(values[2], "Adaptive [%s]", is88KeyMode ? "88 Keys" : "128 Keys");
        }
    } else {
        // Simple display for fixed mode
        sprintf(values[2], "Fixed %s", is88KeyMode ? "88 Keys" : "128 Keys");
    }

    // Add FPS value
    sprintf(values[3], "%d", GetFPS());

    // Note count information (current/total) - add comma separators
    char formattedNotesCount[32];
    char formattedTotalNotes[32];

    // Implement comma separators (insert commas every 3 digits)
    snprintf(formattedNotesCount, sizeof(formattedNotesCount), "%'d", noteStats.notesCount);
    snprintf(formattedTotalNotes, sizeof(formattedTotalNotes), "%'d", noteStats.totalNotes);

    sprintf(values[4], "%s/%s", formattedNotesCount, formattedTotalNotes);

    // NPSを「NPS (Max): X (X)」形式で表示（カンマ区切り）
    char formattedNPS[32], formattedMaxNPS[32];
    snprintf(formattedNPS, sizeof(formattedNPS), "%'d", (int)noteStats.smoothNPS);
    snprintf(formattedMaxNPS, sizeof(formattedMaxNPS), "%'d", (int)noteStats.maxNPS);
    snprintf(values[5], sizeof(values[5]), "%s (%s)", formattedNPS, formattedMaxNPS);

    // Polyphonyを「Poly (Max): X (X)」形式で表示（カンマ区切り）
    char formattedPoly[32], formattedMaxPoly[32];
    snprintf(formattedPoly, sizeof(formattedPoly), "%'d", noteStats.smoothPolyphony);
    snprintf(formattedMaxPoly, sizeof(formattedMaxPoly), "%'d", noteStats.maxPolyphony);
    snprintf(values[6], sizeof(values[6]), "%s (%s)", formattedPoly, formattedMaxPoly);

    // 現在バッファーにあるアクティブなノート数を表示（カンマ区切り）
    extern int getActivePrerenderedNoteCount(void);
    char formattedRendered[32];
    snprintf(formattedRendered, sizeof(formattedRendered), "%'d", getActivePrerenderedNoteCount());
    snprintf(values[7], sizeof(values[7]), "%s", formattedRendered);

    // Calculate maximum label width
    int maxLabelWidth = 0;
    for (int i = 0; i < 8; i++) {  // 配列サイズを8に変更
        int width = MeasureText(labels[i], 16);
        if (width > maxLabelWidth) {
            maxLabelWidth = width;
        }
    }

    // Calculate maximum value width
    int maxValueWidth = 0;
    for (int i = 0; i < 8; i++) {  // 配列サイズを8に変更
        int width = MeasureText(values[i], 16);
        if (width > maxValueWidth) {
            maxValueWidth = width;
        }
    }

    // Calculate total panel width
    int panelWidth = maxLabelWidth + maxValueWidth + 25;

    // Calculate X coordinate for top-right corner placement
    int baseX = screenWidth - panelWidth - margin;

    // Get font size information
    Font defaultFont = GetFontDefault();
    float fontSize = 16.0f;

    // Sample text for text measurement (get representative height)
    Vector2 textSize = MeasureTextEx(defaultFont, "Ty|", fontSize, 0);
    int textHeight = (int)textSize.y;

    // Number of display items
    int itemCount = 8;  // 項目数を8に変更

    // Spacing between items (text height + padding)
    int itemSpacing = textHeight + 4;  // Text height + 4 pixels padding

    // Padding above and below panel
    int verticalPadding = 8;

    // Calculate background height (top padding + total item height and spacing + bottom padding)
    int panelHeight = verticalPadding + (itemCount * itemSpacing) + verticalPadding;

    // Draw semi-transparent black background
    DrawRectangle(baseX - 5, timeY - 5,
                  panelWidth, panelHeight, (Color){0, 0, 0, 180});

    // Draw each item (dynamically calculate text positions)
    for (int i = 0; i < itemCount; i++) {
        int itemY = timeY + verticalPadding + (i * itemSpacing);

        // Draw label (left-aligned) - Raylibの関数を明示的に使用
        Vector2 labelPos = { (float)baseX, (float)itemY };
        // DrawTextExをRaylib関数として明示的に使用
        DrawTextEx(defaultFont, labels[i], labelPos, fontSize, 0, WHITE);

        // Draw value (positioned to the right of the label)
        Color valueColor = WHITE;
        if (i == 1) valueColor = YELLOW;  // BPM is yellow
        if (i == 2) {  // Color coding for view mode
            valueColor = isAdaptiveViewEnabled ? GREEN : WHITE;
            if (isAdaptiveViewEnabled && isTransitioning) valueColor = ORANGE;
        }
        if (i == 3) valueColor = LIME;    // FPS is green
        if (i == 5) valueColor = SKYBLUE; // NPS is light blue
        if (i == 6) valueColor = PINK;    // Polyphony is pink
        if (i == 7) valueColor = MAGENTA; // Rendered count is magenta

        // 値の表示位置もVector2を使用
        Vector2 valuePos = { (float)(baseX + maxLabelWidth + 10), (float)itemY };
        // DrawTextExをRaylib関数として明示的に使用
        DrawTextEx(defaultFont, values[i], valuePos, fontSize, 0, valueColor);
    }
}

// Function to get the height of the status overlay
int getStatusOverlayHeight(void) {
    // Return the standard height of the status overlay
    return 40; // Adjust based on actual height
}
