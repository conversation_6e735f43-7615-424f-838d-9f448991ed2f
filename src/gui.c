#include "raylib_compat.h"  // RaylibとWindowの競合を解決
#include "gui.h"
#include "midi_visualizer.h"
#include "midiplayer/loader.h" // MidiLoadingStatus構造体のインクルード
#include "piano_visualizer.h" // 新しく作成したヘッダーファイル
#include "channel_activity.h" // チャンネルアクティビティ用ヘッダー
#include "setting_window.h"   // 設定ウィンドウ用ヘッダー
#include "midiplayer.h"  // 一時停止/再生機能のためのヘッダーを追加
#include "midiplayer/utils.h" // プログレスバー関連の関数宣言を追加
#include "camera.h"  // カメラモジュールを追加
#include "status_overlay.h" // ステータスオーバーレイのヘッダーを追加
#include "keyboard_display.h" // キーボード表示用ヘッダーを追加

// 歌詞表示用ヘッダーをインクルード
#include "lyrics_display.h"

// Linux固有の機能をインクルード
#ifdef __linux__
#include "linux_utils.h"
#endif

// raylib と raygui のインクルード
#include <raylib.h>
#include <raymath.h>  // Clamp関数のため
#include "rlgl.h"     // rlgl.hを追加

// rayguiの実装ヘッダー（InitRayGUI関数の宣言を含む）
#include "raygui_impl.h"

#include <stdlib.h>  // rand用
#include <time.h>    // time用
#include <locale.h> // setlocale用
#include <string.h> // 文字列操作用

// 必要に応じて stdbool.h が含まれていることを確認
#include <stdbool.h>

// MAX関数を追加
#ifndef MAX
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

// xmakeのbin2cツールで生成したアイコンデータのヘッダーファイルをインクルード
static unsigned char g_png_data[] = {
    #include "HMP-icon.png.h"
};

// xmakeのbin2cツールで生成したNotoSansJP-SemiBold.ttfのヘッダーファイルをインクルード
static unsigned char g_font_data[] = {
	#include "NotoSansJP-SemiBold.ttf.h"
};

// 他のファイルで定義されているグローバル変数を参照
extern bool IS_QUIT;

// main.cで定義されているグローバル変数の参照を追加
extern pthread_mutex_t sync_mutex;
extern pthread_cond_t sync_cond;
extern volatile bool gui_initialized;

bool isMoveCamera = false;
bool isVSync = true;
bool isHiddenUI = false;

// プログレスバーの状態を管理する変数
static struct {
    bool isClicking;         // プログレスバーをクリック中かどうか
    int barHeight;           // プログレスバーの高さ
} progressBarState = {
    .isClicking = false,
    .barHeight = 20
};

// 追加: フォント処理用のグローバル変数ロード画面のときにDrawtextExでフォントを使うようにしてください

// 追加: フォント処理用のグローバル変数
static Font japaneseFont;
static bool japaneseFontLoaded = false;

// ウィンドウタイトル更新用の変数
static struct {
    bool updatePending;
    char filename[256];
} windowTitleState = {
    .updatePending = false,
    .filename = ""
};

// 安全なウィンドウタイトル更新フラグを設定する関数（別スレッドから呼び出し可能）
void setWindowTitleUpdateFlag(const char* filename) {
    windowTitleState.updatePending = true;
    if (filename) {
        strncpy(windowTitleState.filename, filename, sizeof(windowTitleState.filename) - 1);
        windowTitleState.filename[sizeof(windowTitleState.filename) - 1] = '\0';
    } else {
        windowTitleState.filename[0] = '\0';
    }
}

// ウィンドウタイトル更新フラグを取得
bool hasPendingTitleUpdate(void) {
    return windowTitleState.updatePending;
}

// 保存されたファイル名を取得
const char* getPendingTitleFilename(void) {
    return windowTitleState.filename;
}

// ウィンドウタイトル更新フラグをクリア
void clearWindowTitleUpdateFlag(void) {
    windowTitleState.updatePending = false;
}

// 追加: 日本語フォントの初期化関数
void initJapaneseFont(int fontSize) {
    if (japaneseFontLoaded) {
        UnloadFont(japaneseFont);
    }

	// フォントの読み込み
	bool fontLoaded = false;

    // まずメイリオフォントをシステムからロードを試みる
    const char* meiryoPaths[] = {
        "C:/Windows/Fonts/meiryo.ttc",
        "C:/Windows/Fonts/meiryo.ttf",
        "C:/Windows/Fonts/msgothic.ttc" // MSゴシックもフォールバックとして追加
    };

    // システムフォントの読み込みを試みる - size_t型を使用して警告を修正
    for (size_t i = 0; i < sizeof(meiryoPaths) / sizeof(meiryoPaths[0]); i++) {
        FILE* file = fopen(meiryoPaths[i], "rb");
        if (file) {
            fclose(file);
            // フォントが存在する場合、読み込みを試みる
            japaneseFont = LoadFont(meiryoPaths[i]);

            // ロードが成功したか確認
            if (japaneseFont.texture.id > 0) {
                fontLoaded = true;
                printf("日本語システムフォント '%s' をロードしました。\n", meiryoPaths[i]);
                break;
            }
        }
    }

    // システムフォントのロードに失敗した場合、埋め込みフォントを使用
	if (!fontLoaded) {
		// フォントデータをメモリにロード
		int dataSize = sizeof(g_font_data) / sizeof(g_font_data[0]);
		unsigned char* fontData = (unsigned char*)malloc(dataSize);
		if (fontData) {
			memcpy(fontData, g_font_data, dataSize);
			// フォントデータをロード
			japaneseFont = LoadFontFromMemory(".ttf", fontData, dataSize, fontSize, 0, 65535);
			free(fontData);
			if (japaneseFont.texture.id > 0) {
                fontLoaded = true;
                printf("埋め込みフォントをロードしました。\n");
            }
		}
	}

    if (!fontLoaded) {
        fprintf(stderr, "日本語フォントの読み込みに失敗しました。デフォルトフォントを使用します。\n");
        japaneseFont = GetFontDefault();
    }

    japaneseFontLoaded = true;
}

// 追加: 日本語フォントのアンロード関数
void unloadJapaneseFont() {
    if (japaneseFontLoaded) {
        UnloadFont(japaneseFont);
        japaneseFontLoaded = false;
    }
}

// 追加: 日本語フォントを取得する関数
Font getJapaneseFont() {
    if (!japaneseFontLoaded) {
        initJapaneseFont(24); // デフォルトサイズで初期化
    }
    return japaneseFont;
}

// 追加: ウィンドウタイトルを更新する関数
void updateWindowTitle(const char* filename) {
    if (filename == NULL || strlen(filename) == 0) {
        SetWindowTitle("Hachimitsu MIDI Player");
        return;
    }

    char windowTitle[256] = "Hachimitsu MIDI Player: ";
    strncat(windowTitle, filename, 255 - strlen(windowTitle));
    SetWindowTitle(windowTitle);
}

// MIDIローディングプログレスバーを描画する関数
void drawMidiLoadingProgress() {
    // ロード中でなければ何も表示しない
    if (!gMidiLoadingStatus.isLoading && !gMidiLoadingStatus.hasError) {
        return;
    }

    int screenWidth = GetScreenWidth();
    int screenHeight = GetScreenHeight();

    // 半透明のオーバーレイ（モーダル表示のため）
    DrawRectangle(0, 0, screenWidth, screenHeight,
                 (Color){0, 0, 0, 128});  // 半透明の黒

    // ローディングボックスの寸法と位置
    int boxWidth = screenWidth * 0.7;
    int boxHeight = 150;
    int boxX = screenWidth / 2 - boxWidth / 2;
    int boxY = screenHeight / 2 - boxHeight / 2;

    // ローディングボックスの背景
    DrawRectangleRounded((Rectangle){boxX, boxY, boxWidth, boxHeight}, 0.1f, 10,
                         (Color){50, 50, 70, 230});

    // ファイル名のみを表示するよう修正
    int titleFontSize = 20;
    // ファイル名のみ表示
    DrawTextEx(getJapaneseFont(), gMidiLoadingStatus.filename,
              (Vector2){boxX + 20, boxY + 20}, titleFontSize, 1, WHITE);

    // ステータスメッセージをDrawTextExに変更
    DrawTextEx(getJapaneseFont(), gMidiLoadingStatus.statusMessage,
              (Vector2){boxX + 20, boxY + 50}, 16, 1, LIGHTGRAY);

    // トラック情報（もしあれば）
    if (gMidiLoadingStatus.totalTracks > 0) {
        char trackInfo[100];
        sprintf(trackInfo, "Tracks: %d/%d",
                gMidiLoadingStatus.loadedTracks,
                gMidiLoadingStatus.totalTracks);

        // DrawTextからDrawTextExに変更
        DrawTextEx(getJapaneseFont(), trackInfo, (Vector2){boxX + 20, boxY + 75}, 16, 1, LIGHTGRAY);
    }

    // プログレスバーの背景
    DrawRectangle(boxX + 20, boxY + 100, boxWidth - 40, 20, DARKGRAY);

    // プログレスバーの塗りつぶし部分（進行状況に応じて）
    float progressWidth = (boxWidth - 40) * gMidiLoadingStatus.progress;

    Color progressColor = gMidiLoadingStatus.hasError ? RED : SKYBLUE;
    DrawRectangle(boxX + 20, boxY + 100, progressWidth, 20, progressColor);

    // プログレスバーの枠線
    DrawRectangleLines(boxX + 20, boxY + 100, boxWidth - 40, 20, WHITE);

    // 進行状況のパーセント表示
    char percentText[10];
    sprintf(percentText, "%.0f%%", gMidiLoadingStatus.progress * 100);

    // DrawTextからDrawTextExに変更
    Vector2 percentSize = MeasureTextEx(getJapaneseFont(), percentText, 16, 1);
    DrawTextEx(getJapaneseFont(), percentText,
              (Vector2){boxX + boxWidth/2 - percentSize.x/2, boxY + 102}, 16, 1, WHITE);
}

// VSyncのON/OFF切り替え
void toggleVSync() {
	isVSync = !isVSync;
	SetTargetFPS(isVSync ? 60 : 0);
	SetConfigFlags(isVSync ? FLAG_VSYNC_HINT : FLAG_VSYNC_HINT);
}

// 入力をブロックすべき状態かどうかを判定する関数
bool shouldBlockInput() {
    // 設定ウィンドウまたはMIDIロード中またはプログレスバークリック中の場合は入力をブロック
    return showSettingWindow || gMidiLoadingStatus.isLoading || progressBarState.isClicking;
}

// プログレスバークリック処理関数
void handleProgressBarClick() {
    // プログレスバーの領域定義
    Rectangle progressBarRect = { 0, 0, GetScreenWidth(), progressBarState.barHeight };

    // プログレスバークリック判定
    if (IsMouseButtonPressed(MOUSE_LEFT_BUTTON)) {
        if (CheckCollisionPointRec(GetMousePosition(), progressBarRect)) {
            progressBarState.isClicking = true;
        }
    }

    // クリック中（ドラッグ中も含む）の処理
    if (progressBarState.isClicking) {
        float mouseX = GetMousePosition().x;
        float screenWidth = (float)GetScreenWidth();
        float progress = mouseX / screenWidth;

        // プログレスバーの視覚的更新（ドラッグ中も反映）
        if (isMidiLoaded()) {
            // クリック位置に合わせてプログレスを更新し、前の描画状態をリセット
            setMidiProgress(progress);

            // 描画状態をリセット
            resetNoteVisualization();
        }

        // マウスリリース時にクリック状態を解除
        if (IsMouseButtonReleased(MOUSE_LEFT_BUTTON)) {
            progressBarState.isClicking = false;
        }
    }
}

// キーハンドリング関数
void handleKeyInput() {
    // プログレスバークリック処理
    handleProgressBarClick();

    // 設定ウィンドウを開くキー処理
    if (IsKeyPressed(KEY_F1) && !IsMouseButtonDown(MOUSE_LEFT_BUTTON)) {
        toggleSettingWindow();
    }

    // F2キーで128鍵盤モードに固定
    if (IsKeyPressed(KEY_F2) && !IsMouseButtonDown(MOUSE_LEFT_BUTTON)) {
        set88KeyMode(false); // 128鍵モードに設定
        // アダプティブモードを無効化
        isAdaptiveViewEnabled = false;
    }

    // F3キーで88鍵盤モードに固定
    if (IsKeyPressed(KEY_F3) && !IsMouseButtonDown(MOUSE_LEFT_BUTTON)) {
        set88KeyMode(true); // 88鍵モードに設定
        // アダプティブモードを無効化
        isAdaptiveViewEnabled = false;
    }

    // F4キーでアダプティブモードを切り替え
    if (IsKeyPressed(KEY_F4) && !IsMouseButtonDown(MOUSE_LEFT_BUTTON)) {
        toggleAdaptiveView();
    }

    // スペースキーで再生/一時停止切り替え
    if (IsKeyPressed(KEY_SPACE)) {
        toggleMidiPlayPause();
    }

    // Rキーで最初から再生（Restart）
    if (IsKeyPressed(KEY_R) && isMidiLoaded()) {
        // プログレスを0にセット（曲の先頭に戻す）
        setMidiProgress(0.0f);
        resetNoteVisualization();  // 描画状態をリセット

        // 一時停止中だった場合は再生を開始
        if (isMidiPaused()) {
            toggleMidiPlayPause();
        }
    }

    // Lキーで相対時間モードの切り替え（Local time）
    if (IsKeyPressed(KEY_L) && isMidiLoaded()) {
        if (isRelativeTimeMode()) {
            setRelativeTimeMode(false);  // 相対時間モードを無効化
        } else {
            setRelativeTimeMode(true);   // 相対時間モードを有効化
        }
    }

    // BPM調整キー：+/-キーでBPMを10%ずつ増減
    if (isMidiLoaded()) {
        float currentBPM = getCurrentBPM();
        float currentMultiplier = currentBPM / (60000000.0f / (float)gMidiFile->CurrentTempo);

        // プラスキーで10%速く
        if (IsKeyPressed(KEY_EQUAL) || IsKeyPressed(KEY_KP_ADD)) {
            float newMultiplier = currentMultiplier * 1.1f;
            setPlaybackBPM(newMultiplier);
        }

        // マイナスキーで10%遅く
        if (IsKeyPressed(KEY_MINUS) || IsKeyPressed(KEY_KP_SUBTRACT)) {
            float newMultiplier = currentMultiplier * 0.9f;
            setPlaybackBPM(newMultiplier);
        }

        // [と]キーで5%ずつ調整
        if (IsKeyPressed(KEY_LEFT_BRACKET)) {
            float newMultiplier = currentMultiplier * 0.95f;
            setPlaybackBPM(newMultiplier);
        }

        if (IsKeyPressed(KEY_RIGHT_BRACKET)) {
            float newMultiplier = currentMultiplier * 1.05f;
            setPlaybackBPM(newMultiplier);
        }

        // 0キーで標準速度に戻す
        if (IsKeyPressed(KEY_ZERO) || IsKeyPressed(KEY_KP_0)) {
            setPlaybackBPM(1.0f);
        }
    }

    // 左右矢印キーで早送り・巻き戻し
    if (isMidiLoaded()) {
        float currentProgress = getMidiProgress();
        float jumpAmount = 0.05f; // 5%ずつジャンプ

        // 左矢印キーで巻き戻し
        if (IsKeyPressed(KEY_LEFT)) {
            float newProgress = currentProgress - jumpAmount;
            setMidiProgress(newProgress);
            resetNoteVisualization(); // 描画状態をリセット
        }

        // 右矢印キーで早送り
        if (IsKeyPressed(KEY_RIGHT)) {
            float newProgress = currentProgress + jumpAmount;
            setMidiProgress(newProgress);
            resetNoteVisualization(); // 描画状態をリセット
        }

        // Homeキーで最初に戻る
        if (IsKeyPressed(KEY_HOME)) {
            setMidiProgress(0.0f);
            resetNoteVisualization(); // 描画状態をリセット
        }

        // Endキーで最後に移動
        if (IsKeyPressed(KEY_END)) {
            setMidiProgress(1.0f);
            resetNoteVisualization(); // 描画状態をリセット
        }
    }

	// マウスの左クリックが押されている間だけカメラを更新
	if (IsMouseButtonDown(MOUSE_LEFT_BUTTON)) {
		updateCamera();
	}

	// VSyncのON/OFF切り替え
	if (IsKeyPressed(KEY_V)) {
		toggleVSync();
	}

	// UIの表示切り替え
	if (IsKeyPressed(KEY_H)) {
		isHiddenUI = !isHiddenUI;
	}
}

// 新しい関数: ノートの視覚化状態をリセットする
void resetNoteVisualization() {
    // ピアノ可視化状態をリセット
    resetPianoVisualizer();

    // MIDI可視化状態をリセット
    resetMidiVisualizer();
}

// MIDIプレイヤーのプログレスバーを描画する関数
void drawMidiProgressBar() {
    int screenWidth = GetScreenWidth();
    int barHeight = progressBarState.barHeight; // 共有変数を使用

    // プログレスバーの外枠（上部に固定）- 常に表示
    DrawRectangle(0, 0, screenWidth, barHeight, (Color){40, 40, 40, 255});

    // MIDIファイルが読み込まれていない場合はプログレスバーの枠のみ表示
    if (!isMidiLoaded()) {
        // プログレスバーの枠線のみ表示
        DrawRectangleLines(0, 0, screenWidth, barHeight, (Color){80, 80, 80, 255});

        // 「No MIDI file loaded」などのメッセージを表示
        const char* msg = "No MIDI file loaded";
        int textWidth = MeasureText(msg, 16);
        DrawText(msg, screenWidth / 2 - textWidth / 2, 2, 16, LIGHTGRAY);
        return;
    }

    // プログレスバーの塗りつぶし部分
    float progress = getMidiProgress(); // 0.0～1.0の値を取得
    int progressWidth = (int)(screenWidth * progress);

    // 一時停止中は違う色で表示
    Color progressColor = isMidiPaused() ? (Color){150, 150, 150, 255} : (Color){0, 120, 255, 255};
    DrawRectangle(0, 0, progressWidth, barHeight, progressColor);

    // プログレスバーの枠線
    DrawRectangleLines(0, 0, screenWidth, barHeight, WHITE);

    // 再生時間の表示を追加（右側に配置）
    char timeString[16];
    char totalTimeString[16];
    getMidiTimeString(timeString, sizeof(timeString), false);  // 現在時間
    getMidiTimeString(totalTimeString, sizeof(totalTimeString), true);  // 総時間

    // 相対時間モードの場合、表示を変更
    char timeDisplay[64]; // バッファサイズを32から64に増加して警告を解消
    if (isRelativeTimeMode()) {
        snprintf(timeDisplay, sizeof(timeDisplay), "%s / %s (REL)", timeString, totalTimeString);
    } else {
        snprintf(timeDisplay, sizeof(timeDisplay), "%s / %s", timeString, totalTimeString);
    }

    // プログレスバーの下に操作方法を小さく表示
    if (isMidiLoaded()) {
        const char* helpText = "Left/Right: Seek | R: Restart | L: Relative Time | +/-: Speed | 0: Reset Speed | F2: 128 Keys | F3: 88 Keys | F4: Adaptive Mode";
        int textWidth = MeasureText(helpText, 10);
        DrawText(helpText, screenWidth / 2 - textWidth / 2, barHeight + 2, 10, LIGHTGRAY);
    }
}

// ピアノロール表示のための設定
bool showPianoRoll = true;  // デフォルトを有効に変更

// ピアノロール表示を更新する関数
void updatePianoRollView() {
    // プリレンダリングされたノートからピアノロールを描画
    drawPianoRollFromPrerenderedNotes();
}

// GUI設定メニューの実装箇所に以下のコードを追加
void drawSettingsMenu() {
    // ...existing code...

    // ピアノロール表示オプションを追加
    if (GuiCheckBox((Rectangle){ GetScreenWidth() - 200, GetScreenHeight() - 100, 15, 15 }, "ピアノロール表示", &showPianoRoll)) {
        // ピアノロール表示の切替時に必要な処理をここに追加（必要に応じて）
    }

    // ...existing code...
}

// GUIの描画関数内で呼び出す部分（適切な場所に追加する）
void drawGui() {
    // ...existing code...

    // ピアノロールビューのオプションが有効な場合、ピアノロールを描画
    if (showPianoRoll) {
        updatePianoRollView();
    }

    // ...existing code...
}

// GUI要素を描画する関数
void drawGUIElements() {
    if (!isHiddenUI) {
		// プログレスバーを描画（最初に描画して他の要素の下に隠れないようにする）
		drawMidiProgressBar();

		// ステータスオーバーレイを描画（プログレスバーの下に配置）
        drawStatusOverlay();

        // キーボード2D表示を描画（追加）
        drawKeyboardDisplay();

        // チャンネルアクティビティ表示
        drawChannelActivity();

        // カメラデバッグ情報を表示
        drawCameraDebugInfo();

        // 歌詞とマーカーを表示（3D表示の上に重ねる）
        drawLyricsDisplay();
    }

    // 以下の要素はisHiddenUIに関わらず常に表示
    // MIDIローディングプログレスバーを表示（ロード中の場合のみ）
    drawMidiLoadingProgress();

    // 設定ウィンドウを描画（UIが非表示でも常に表示）
    drawSettingWindow();

    // 一時停止中は画面に「PAUSED」と表示（3D表示の上に重ねて表示）
    if (isMidiPaused()) {
        int screenWidth = GetScreenWidth();
        const char* pausedText = "PAUSED";
        int textWidth = MeasureText(pausedText, 40);

        // 半透明の背景を描画して「PAUSED」テキストを目立たせる
        DrawRectangle(screenWidth/2 - textWidth/2 - 10, 65, textWidth + 20, 50, (Color){0, 0, 0, 128});
        DrawText(pausedText, screenWidth/2 - textWidth/2, 70, 40, RED);
    }
}

// GUIスレッド関数 - フレームレート非依存版
void* gui_thread_function() {
    // チャンネル色の初期化
    initChannelColors();

    setlocale(LC_ALL, "ja_JP.UTF-8");

    // アンチエイリアシングを有効にする
    SetConfigFlags(FLAG_MSAA_4X_HINT);

    // 初期ウィンドウタイトルを設定（ファイル名なし）
    InitWindow(1280, 720, "Hachimitsu MIDI Player");

    // フレームレート制御 - VSyncが有効なら60FPS、無効なら無制限
    SetTargetFPS(isVSync ? 60 : 0);
    SetConfigFlags(isVSync ? FLAG_VSYNC_HINT : FLAG_VSYNC_HINT);

    // アイコンを設定
    Image iconImage = LoadImageFromMemory(".png", g_png_data, sizeof(g_png_data));
    SetWindowIcon(iconImage);
    UnloadImage(iconImage);  // 使用後はリソースを解放

    // rayguiの初期化
    InitRayGUI();

    // 3Dカメラの初期化
    initCamera();

    // アダプティブビューの初期化を追加
    initAdaptiveView();

    // キー状態の初期化を追加
    initKeyStates();

    // 日本語フォントの初期化
    initJapaneseFont(24);

    // キーボード表示の初期化
    static bool keyboardDisplayInitialized = false;

    // GUI初期化完了を通知
    pthread_mutex_lock(&sync_mutex);
    gui_initialized = true;
    pthread_cond_signal(&sync_cond);
    pthread_mutex_unlock(&sync_mutex);

    // 前回のフレーム時間を記録（デルタタイム計算用）
    float lastFrameTime = GetTime();

    // アニメーション時間累積変数
    float animAccumulator = 0.0f;

    // メインループ
    while (!WindowShouldClose() && !IS_QUIT) {
        // フレームタイム計測
        float currentTime = GetTime();
        float deltaTime = currentTime - lastFrameTime;
        lastFrameTime = currentTime;

        // 時間累積（アニメーション用）- 最大値を制限して極端なスキップを防止
        animAccumulator += (deltaTime > 0.1f) ? 0.1f : deltaTime;

        if (WindowShouldClose()) {
            IS_QUIT = true;
        }

        // 初期化処理の中にキーボード表示の初期化を追加
        if (!keyboardDisplayInitialized) {
            initKeyboardDisplay();
            keyboardDisplayInitialized = true;
        }

        // ウィンドウタイトル更新フラグがあれば処理
        if (hasPendingTitleUpdate()) {
            updateWindowTitle(getPendingTitleFilename());
            clearWindowTitleUpdateFlag();

            // ウィンドウタイトル更新時にBPM情報も更新（新しいファイルが読み込まれた場合）
            updateBpmInfo();  // 関数は残すが、外部関数を呼び出すように変更
        }

        // 入力ブロッキングの状態を取得
        bool blockInput = shouldBlockInput();

        // 入力がブロックされていない場合のみキー処理を行う
        if (!blockInput) {
            handleKeyInput();
        } else {
            // ブロッキング状態でもプログレスバークリック処理は行う
            handleProgressBarClick();
        }

        // ノートデータの更新 - 入力ブロックとは独立して常に更新
        updateNoteData();

        // アダプティブビューの処理
        // MIDIが読み込まれていて、アダプティブモードが有効な場合のみチェック
        if (isMidiLoaded() && isAdaptiveViewEnabled) {
            // マウス操作中はアダプティブビューの検出を一時停止
            if (!IsMouseButtonDown(MOUSE_LEFT_BUTTON)) {
                // 最初にノート範囲をチェックしてモード変更が必要か判断
                checkNoteRangeAndUpdateCamera();
            }
            // トランジションの更新は毎フレーム行う（スムーズな動きのため）
            updateCameraTransition();
        }

        // MIDIファイルが再生中なら歌詞表示も更新
        if (isMidiLoaded()) {
            updateLyricsDisplay(gMidiFile->CurrentTick);
        }

        // 描画処理
        BeginDrawing();
            ClearBackground(BLACK);

            // 3D描画モード - 一時停止中も描画する
            BeginMode3D(camera);
                draw3DPianoKeyboard();
            EndMode3D();

            drawGUIElements();
        EndDrawing();
    }

    // raylibのクリーンアップ
    CloseWindow();

    // プロセス終了
    exit(0);

    return NULL;
}

