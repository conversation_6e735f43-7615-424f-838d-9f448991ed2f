#include "camera.h"
#include "rlgl.h"
#include <raymath.h>
#include <stdio.h>
#include "midi_visualizer.h" // ノートデータにアクセスするため追加

// グローバル変数の定義
Camera3D camera;
bool isMouseMove = false;
bool is88KeyMode = false; // 88鍵盤モードのフラグ（デフォルトはfalse）

// アダプティブビュー関連の変数
bool isAdaptiveViewEnabled = true; // デフォルトで有効
float cameraTransitionProgress = 0.0f; // 0.0=88キーモード、1.0=128キーモード
bool isTransitioning = false; // トランジション中フラグ
float transitionSpeed = 0.05f; // トランジションの速度
bool targetIs128KeyMode = false; // トランジションの目標モード（true=128キー）

// 88鍵の範囲定義 (A0-C8、MIDI番号 21-108)
#define PIANO_MIN_NOTE 21
#define PIANO_MAX_NOTE 108

// 標準の128鍵盤用カメラ設定
Vector3 cameraPosition = (Vector3){ 0.0f, 107.0f, 227.0f };  // キーボードの上から見下ろす位置
Vector3 cameraTarget = (Vector3){ 0.0f, 58.0f, 9.0f };     // 原点（キーボードの中心）をターゲットに
Vector3 cameraUp = (Vector3){ 0.0f, 1.0f, 0.0f };

// 88鍵盤用カメラ設定（中央の88鍵に焦点を当てた位置）
Vector3 camera88Position = (Vector3){ 2.16f, 82.5f, 160.0f };  // より近く低い位置
Vector3 camera88Target = (Vector3){ 2.16f, 32.0f, 18.0f };      // ターゲットは同じ
Vector3 camera88Up = (Vector3){ 0.0f, 1.0f, 0.0f };           // Up方向は同じ

// ノート範囲外検出時の履歴を追加
static struct {
    bool detectedOutsideNotes;    // 88鍵範囲外のノートを検出したか
    int stabilityCounter;         // 状態安定カウンター
    float cooldownTimer;          // クールダウンタイマー
} noteRangeState = {
    .detectedOutsideNotes = false,
    .stabilityCounter = 0,
    .cooldownTimer = 0.0f
};

// カメラの初期化関数
void initCamera(void) {
    // カメラの初期化（デフォルトは128鍵モード）
    camera.position = cameraPosition;
    camera.target = cameraTarget;
    camera.up = cameraUp;
    camera.fovy = 45.0f;
    camera.projection = CAMERA_PERSPECTIVE;

    // より遠くまで見えるように調整
    rlSetClipPlanes(1.0, 10000.0);  // クリッピングプレーンを設定
}

// 88鍵盤モードを直接設定する関数（既存関数）
void set88KeyMode(bool enable) {
    if (is88KeyMode == enable) return; // 状態が同じなら何もしない

    is88KeyMode = enable;

    // アダプティブモードが有効でトランジション中でない場合は直接切り替え
    if (!isAdaptiveViewEnabled || !isTransitioning) {
        // モードに応じたカメラ位置に設定
        if (is88KeyMode) {
            camera.position = camera88Position;
            camera.target = camera88Target;
            camera.up = camera88Up;
        } else {
            camera.position = cameraPosition;
            camera.target = cameraTarget;
            camera.up = cameraUp;
        }
        camera.fovy = 45.0f;
    }
}

// 88鍵盤モードの切り替え関数（既存関数の修正）
void toggle88KeyMode(void) {
    // アダプティブモードを無効化
    isAdaptiveViewEnabled = false;

    // トランジション中のフラグをリセット
    isTransitioning = false;

    // 通常の切り替え
    set88KeyMode(!is88KeyMode);
}

// アダプティブビューモードの切り替え
void toggleAdaptiveView(void) {
    isAdaptiveViewEnabled = !isAdaptiveViewEnabled;
    printf("アダプティブビューモード: %s\n", isAdaptiveViewEnabled ? "有効" : "無効");
}

// ノート範囲をチェックして88鍵の範囲外のノートがあるか確認
bool isNoteOutsidePianoRange(void) {
    if (!gVisualizer.initialized) return false;

    bool hasOutsideNotes = false;

    pthread_mutex_lock(&gVisualizer.mutex);
    for (int ch = 0; ch < 16; ch++) {
        // ドラムチャンネル（10チャンネル, 0-based indexで9）は除外
        if (ch == 9) continue;

        for (int note = 0; note < 128; note++) {
            if (gVisualizer.notes[ch][note].active) {
                // ノートが88鍵の範囲外かどうかチェック
                if (note < PIANO_MIN_NOTE || note > PIANO_MAX_NOTE) {
                    hasOutsideNotes = true;
                    // 範囲外のノートが検出されたらメッセージを表示
                    //printf("範囲外のノート検出: チャンネル=%d, ノート番号=%d\n", ch, note);
                    break;
                }
            }
        }
        if (hasOutsideNotes) break;
    }
    pthread_mutex_unlock(&gVisualizer.mutex);

    return hasOutsideNotes;
}

// ノート範囲をチェックしてカメラモードを更新（安定化ロジック追加）
void checkNoteRangeAndUpdateCamera(void) {
    // アダプティブビューが無効なら何もしない
    if (!isAdaptiveViewEnabled) return;

    // 既にトランジション中なら処理をスキップ
    if (isTransitioning) return;

    // 1フレームごとのデルタタイム（60FPSを前提として約0.0167秒）
    float deltaTime = 1.0f / 60.0f;

    // クールダウンタイマーを更新
    if (noteRangeState.cooldownTimer > 0) {
        noteRangeState.cooldownTimer -= deltaTime;
    }

    // 88鍵範囲外のノートがあるかチェック
    bool currentOutsideNotes = isNoteOutsidePianoRange();

    // 現在の状態が前回の状態と異なる場合、安定カウンターをリセット
    if (currentOutsideNotes != noteRangeState.detectedOutsideNotes) {
        noteRangeState.stabilityCounter = 0;
        noteRangeState.detectedOutsideNotes = currentOutsideNotes;
    } else {
        // 同じ状態が続いている場合、カウンターを増加
        noteRangeState.stabilityCounter++;
    }

    // 状態が安定している（5フレーム以上同じ状態）かつクールダウンが終了している場合のみ切り替えを検討
    if (noteRangeState.stabilityCounter >= 5 && noteRangeState.cooldownTimer <= 0) {
        bool needsChange = false;

        // 88鍵範囲外のノートがあり、現在88鍵モードならトランジション開始
        if (currentOutsideNotes && is88KeyMode) {
            needsChange = true;
            targetIs128KeyMode = true;
            printf("アダプティブビュー: 88鍵→128鍵へのトランジション開始（範囲外のノートを検出）\n");
        }
        // 88鍵範囲外のノートがなく、現在128鍵モードならトランジション開始
        else if (!currentOutsideNotes && !is88KeyMode) {
            needsChange = true;
            targetIs128KeyMode = false;
            printf("アダプティブビュー: 128鍵→88鍵へのトランジション開始（通常範囲のノートのみ）\n");
        }

        if (needsChange) {
            isTransitioning = true;
            // トランジション後にすぐ戻らないようクールダウンタイマーを設定
            noteRangeState.cooldownTimer = 2.0f; // 2秒間のクールダウン
            // 安定カウンターをリセット
            noteRangeState.stabilityCounter = 0;
        }
    }
}

// カメラのトランジションを更新
void updateCameraTransition(void) {
    if (!isTransitioning) return;

    // 1フレームごとのデルタタイム（60FPSを前提として約0.0167秒）
    float deltaTime = 1.0f / 60.0f;

    if (targetIs128KeyMode) {
        // 88鍵→128鍵へトランジション
        cameraTransitionProgress += transitionSpeed * deltaTime * 60.0f; // フレームレート非依存に調整
        if (cameraTransitionProgress >= 1.0f) {
            cameraTransitionProgress = 1.0f;
            isTransitioning = false;
            is88KeyMode = false;
            printf("アダプティブビュー: 128鍵モードに切り替え完了\n");
        }
    } else {
        // 128鍵→88鍵へトランジション
        cameraTransitionProgress -= transitionSpeed * deltaTime * 60.0f; // フレームレート非依存に調整
        if (cameraTransitionProgress <= 0.0f) {
            cameraTransitionProgress = 0.0f;
            isTransitioning = false;
            is88KeyMode = true;
            printf("アダプティブビュー: 88鍵モードに切り替え完了\n");
        }
    }

    // トランジション進行度に基づいてカメラ位置を補間
    camera.position = Vector3Lerp(camera88Position, cameraPosition, cameraTransitionProgress);
    camera.target = Vector3Lerp(camera88Target, cameraTarget, cameraTransitionProgress);
    // Upベクトルは同じなので補間不要
}

// カメラの更新関数
void updateCamera(void) {
    // マウスの動きに基づいてカメラを更新
    UpdateCamera(&camera, CAMERA_FREE);

    // アダプティブビューのチェックと更新
    if (isAdaptiveViewEnabled) {
        checkNoteRangeAndUpdateCamera();
        updateCameraTransition();
    }
}

// カメラを初期位置にリセットする関数
void resetCamera(void) {
    // 現在のモードに応じたカメラ位置にリセット
    if (is88KeyMode) {
        camera.position = camera88Position;
        camera.target = camera88Target;
        camera.up = camera88Up;
    } else {
        camera.position = cameraPosition;
        camera.target = cameraTarget;
        camera.up = cameraUp;
    }
    camera.fovy = 45.0f;

    // トランジション状態もリセット
    isTransitioning = false;
    cameraTransitionProgress = is88KeyMode ? 0.0f : 1.0f;
}

// アダプティブビューを初期化する関数を追加
void initAdaptiveView(void) {
    // アダプティブビュー関連の状態をリセット
    noteRangeState.detectedOutsideNotes = false;
    noteRangeState.stabilityCounter = 0;
    noteRangeState.cooldownTimer = 0.0f;

    // トランジション関連の状態も初期化
    isTransitioning = false;
    cameraTransitionProgress = is88KeyMode ? 0.0f : 1.0f;
}

// カメラデバッグ情報を表示する関数
void drawCameraDebugInfo(void) {
    char posText[100];
    char targetText[100];
    char upText[100];
    char fovyText[50];
    char modeText[50];
    char adaptiveText[50];

    // カメラ位置情報をフォーマット
    sprintf(posText, "Camera Position: X:%.2f Y:%.2f Z:%.2f",
            camera.position.x, camera.position.y, camera.position.z);
    sprintf(targetText, "Camera Target: X:%.2f Y:%.2f Z:%.2f",
            camera.target.x, camera.target.y, camera.target.z);
    sprintf(upText, "Camera Up: X:%.2f Y:%.2f Z:%.2f",
            camera.up.x, camera.up.y, camera.up.z);
    sprintf(fovyText, "Camera FOV: %.2f", camera.fovy);
    sprintf(modeText, "Keyboard Mode: %s", is88KeyMode ? "88 Keys" : "128 Keys");
    sprintf(adaptiveText, "Adaptive View: %s %s %.1f%%",
            isAdaptiveViewEnabled ? "ON" : "OFF",
            isTransitioning ? "[Transitioning]" : "",
            cameraTransitionProgress * 100.0f);

    // 画面左下に表示
    DrawText(posText, 10, 160, 16, GREEN);
    DrawText(targetText, 10, 140, 16, GREEN);
    DrawText(upText, 10, 120, 16, GREEN);
    DrawText(fovyText, 10, 100, 16, GREEN);
    DrawText(modeText, 10, 80, 16, YELLOW);
    DrawText(adaptiveText, 10, 60, 16, YELLOW); // アダプティブモード表示を追加
}
