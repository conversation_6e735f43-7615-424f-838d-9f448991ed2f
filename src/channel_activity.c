#include "channel_activity.h"
#include "midi_visualizer.h"
#include <raylib.h>
#include <stdlib.h>
#include <stdio.h>  // sprintf用に追加
#include <time.h>

// MIDIチャンネル用のカラー配列
Color channelColors[16];

// extern宣言に変更（定義ではなく参照）
extern int channelPitchBends[16];

// チャンネル色の初期化
void initChannelColors() {
    // 乱数初期化を確認
    static bool initialized = false;
    if (!initialized) {
        srand(time(NULL));
        initialized = true;
    }

    // 各チャンネルの色をランダムに初期化
    for (int i = 0; i < 16; i++) {
        channelColors[i] = (Color){
            rand() % 200 + 55,  // R: 暗すぎない色に
            rand() % 200 + 55,  // G
            rand() % 200 + 55,  // B
            255                 // Alpha
        };
    }
}

// ピッチベンド値の設定
void setChannelPitchBend(int channel, int value) {
    if (channel >= 0 && channel < 16) {
        channelPitchBends[channel] = value;
    }
}

// チャンネルアクティビティ表示
void drawChannelActivity() {
    // 更新間隔の制限を削除（毎フレーム更新）

    const int boxSize = 20;
    const int spacing = 5;
    const int startX = 10; // ピアノと同じ左余白
    const int startY = 30; // FPSの下に配置
    const int maxPitchBendMove = 15; // ピッチベンドによる最大移動量（ピクセル）

    pthread_mutex_lock(&gVisualizer.mutex);
    for (int ch = 0; ch < 16; ch++) {
        int x = startX + ch * (boxSize + spacing);

        // ピッチベンド値に基づいて位置を調整
        // ピッチベンドの標準範囲: 0-16383、中央: 8192
        float pitchBendFactor = (channelPitchBends[ch] - 8192) / 8192.0f;
        int yOffset = (int)(pitchBendFactor * maxPitchBendMove);
        int y = startY - yOffset; // 上がプラス方向になるので符号を反転

        // チャンネルが使用されているか確認
        bool isActive = false;
        for (int note = 0; note < 128; note++) {
            if (gVisualizer.notes[ch][note].active) {
                isActive = true;
                break;
            }
        }

        // チャンネルボックスを描画
        Rectangle boxRect = { x, y, boxSize, boxSize };
        if (isActive) {
            DrawRectangleRec(boxRect, channelColors[ch]);
        } else {
            DrawRectangleRec(boxRect, LIGHTGRAY);
        }
        DrawRectangleLinesEx(boxRect, 1, BLACK);

        // チャンネル番号を表示
        char numStr[3];
        sprintf(numStr, "%d", ch + 1);
        DrawText(numStr, x + boxSize/2 - MeasureText(numStr, 10)/2, y + boxSize/2 - 5, 10, BLACK);
    }
    pthread_mutex_unlock(&gVisualizer.mutex);
}
