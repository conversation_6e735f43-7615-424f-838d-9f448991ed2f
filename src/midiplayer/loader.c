#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <pthread.h>
#include <errno.h>
#include <math.h> // fmod関数のために追加

// 内部実装としてマーク
#define INTERNAL_MIDIPLAYER_IMPLEMENTATION
#include "midiplayer.h"
#include "midiplayer/loader.h"
#include "midiplayer/player.h"
#include "midiplayer/utils.h"  // TempoMap構造体の定義のため
#include "thread_args.h"
#include "midi_utils.h"
#include "midi_header.h"

// グローバル変数の定義
MidiLoadingStatus gMidiLoadingStatus = {
    .filename = "",
    .progress = 0.0f,
    .totalTracks = 0,
    .loadedTracks = 0,
    .statusMessage = "Idle",  // Changed from "アイドル状態"
    .isLoading = false,
    .hasError = false,
    .totalTimeMs = 0          // 全体時間を初期化
};

// Windows環境の場合はグローバル変数を定義
#ifdef _WIN32
extern wchar_t gWideFilePath[MAX_PATH];
extern bool gUseWidePath;
#endif

// 進行状況を更新する関数
static void updateLoadingStatus(const char* message, float progress) {
    strncpy(gMidiLoadingStatus.statusMessage, message, 255);
    gMidiLoadingStatus.statusMessage[255] = '\0';
    gMidiLoadingStatus.progress = progress;
}

// 新しい関数: MIDIファイルのテンポイベントを事前スキャン
void scanTempoEventsInMidiFile(MIDIFile *midiFile) {
    if (!midiFile || !midiFile->Data || midiFile->TrackCount <= 0) {
        fprintf(stderr, "Error: Invalid MIDI file for tempo scanning\n");
        return;
    }

    fprintf(stderr, "Scanning MIDI file for tempo events...\n");

    // 各トラックを個別にスキャン
    for (int i = 0; i < midiFile->TrackCount; i++) {
        updateLoadingStatus("Scanning tempo events...", 0.85f + (i * 0.1f / midiFile->TrackCount));

        Track *track = &midiFile->Tracks[i];
        if (track->Ended) continue;

        // 元のヘッドポインタを保存
        uint8_t *originalHead = track->head;
        uint64_t currentTick = 0;
        uint8_t runningStatus = 0;
        bool trackEnded = false;

        // トラックをスキャンしてテンポ変更イベントを探す
        while (!trackEnded && track->head < midiFile->Data + midiFile->DataLength) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
                byte = *track->head++;
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            if (trackEnded) break;

            // 現在のティック位置を更新
            currentTick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (track->head >= midiFile->Data + midiFile->DataLength) {
                trackEnded = true;
                break;
            }

            if (*track->head & 0x80) {
                statusByte = *track->head++;
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // メタイベント処理
            if (statusByte == 0xFF) {
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }

                uint8_t metaType = *track->head++;

                // 可変長バイト長を読み取る
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // テンポ変更イベント (0x51)
                if (metaType == 0x51 && length >= 3 &&
                    track->head + 2 < midiFile->Data + midiFile->DataLength) {
                    // テンポ値を読み取る (24-bit値)
                    uint32_t tempo = ((uint32_t)track->head[0] << 16) |
                                     ((uint32_t)track->head[1] << 8) |
                                     ((uint32_t)track->head[2]);

                    if (tempo > 0) {
                        float bpm = 60000000.0f / (float)tempo;
                        (void)bpm; // 未使用変数の警告を抑制
                        //fprintf(stderr, "Found tempo event at tick %llu: %u (%.2f BPM)\n",
                        //        (unsigned long long)currentTick, tempo, bpm);

                        // テンポマップにテンポ変更を記録
                        addTempoChange(currentTick, tempo);
                    }
                }

                // エンドオブトラックイベント
                else if (metaType == 0x2F) {
                    trackEnded = true;
                    break;
                }

                // メタイベントのデータ部をスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            }
            // SysExイベント
            else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            }
            // チャンネルメッセージ
            else if (statusByte < 0xF0) {
                int msgType = statusByte & 0xF0;

                // データサイズに基づきスキップ
                if (msgType == 0xC0 || msgType == 0xD0) {
                    track->head += 1; // 1バイトデータ
                } else {
                    track->head += 2; // 2バイトデータ
                }

                if (track->head > midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
            }
        }

        // ヘッドポインタを元の位置に戻す
        track->head = originalHead;
    }

    // テンポマップをソートして、テンポ情報をデバッグ出力
    sortTempoMap();
    debugPrintTempoMap();

    // fprintf(stderr, "Tempo scanning completed: %d tempo changes found\n", tempoMap.count);
}

// 追加: MIDIファイルのノート数をカウントして初期化する関数
void countNotesInMidiFile(MIDIFile *midiFile) {
    if (!midiFile || !midiFile->Data || midiFile->TrackCount <= 0) {
        return;
    }

    fprintf(stderr, "Counting total notes in MIDI file...\n");
    int totalNotes = 0;

    // 各トラックを個別にスキャン
    for (int i = 0; i < midiFile->TrackCount; i++) {
        Track *track = &midiFile->Tracks[i];
        if (track->Ended) continue;

        // 元のヘッドポインタを保存
        uint8_t *originalHead = track->head;
        uint64_t currentTick = 0;
        uint8_t runningStatus = 0;
        bool trackEnded = false;

        // トラックをスキャンしてノートイベントをカウント
        while (!trackEnded && track->head < midiFile->Data + midiFile->DataLength) {
            // デルタタイムを読み取る
            uint32_t deltaTime = 0;
            uint8_t byte;

            do {
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
                byte = *track->head++;
                deltaTime = (deltaTime << 7) | (byte & 0x7F);
            } while (byte & 0x80);

            if (trackEnded) break;

            // 現在のティック位置を更新
            currentTick += deltaTime;

            // ステータスバイト処理
            uint8_t statusByte;
            if (track->head >= midiFile->Data + midiFile->DataLength) {
                trackEnded = true;
                break;
            }

            if (*track->head & 0x80) {
                statusByte = *track->head++;
                runningStatus = statusByte;
            } else {
                statusByte = runningStatus;
            }

            // ノートONイベント (0x90) でベロシティが0より大きい場合のみカウント
            if ((statusByte & 0xF0) == 0x90) {
                if (track->head + 1 < midiFile->Data + midiFile->DataLength) {
                    uint8_t velocity = *(track->head + 1);
                    if (velocity > 0) {
                        // ノートONのみカウント (ベロシティ > 0)
                        totalNotes++;
                    }
                }
            }

            // データスキップ - メッセージタイプによって適切にスキップ
            if (statusByte < 0xF0) {
                // チャンネルメッセージ
                if ((statusByte & 0xF0) == 0xC0 || (statusByte & 0xF0) == 0xD0) {
                    // プログラムチェンジ、チャンネルプレッシャー: 1バイト
                    track->head += 1;
                } else {
                    // その他のチャンネルメッセージ: 2バイト
                    track->head += 2;
                }
            } else if (statusByte == 0xFF) {
                // メタイベント
                if (track->head >= midiFile->Data + midiFile->DataLength) {
                    trackEnded = true;
                    break;
                }
                uint8_t metaType = *track->head++;

                // 長さを読み取る
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // エンドオブトラック
                if (metaType == 0x2F) {
                    trackEnded = true;
                    break;
                }

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            } else if (statusByte == 0xF0 || statusByte == 0xF7) {
                // SysExイベント
                uint32_t length = 0;
                do {
                    if (track->head >= midiFile->Data + midiFile->DataLength) {
                        trackEnded = true;
                        break;
                    }
                    byte = *track->head++;
                    length = (length << 7) | (byte & 0x7F);
                } while (byte & 0x80);

                if (trackEnded) break;

                // データをスキップ
                if (track->head + length <= midiFile->Data + midiFile->DataLength) {
                    track->head += length;
                } else {
                    trackEnded = true;
                    break;
                }
            }
        }

        // ヘッドポインタを元の位置に戻す
        track->head = originalHead;
    }

    // 外部関数を呼び出して総ノート数を設定
    extern void setTotalNotes(int count);
    setTotalNotes(totalNotes);
    fprintf(stderr, "Counted a total of %d note-on events in MIDI file\n", totalNotes);
}

// ファイル読み込み時に呼び出される初期化関数
void initializePlaybackState(void) {
    fprintf(stderr, "Initializing playback state...\n");

    // テンポマップを初期化
    initTempoMap();

    // プログラム状態を初期化
    initProgramState();
    fprintf(stderr, "Program state initialized\n");

    // 他の初期化処理...
}

void *loadMidiFile(void *context)
{
    struct midiPlayer_load_args *args = (struct midiPlayer_load_args *)context;
    char *filepath = args->midiFile;
    // --- 修正ここから ---
    char *slash = strrchr(filepath, '/');
    char *backslash = strrchr(filepath, '\\');
    char *filename = NULL;
    if (slash && backslash) {
        filename = (slash > backslash ? slash : backslash) + 1;
    } else if (slash) {
        filename = slash + 1;
    } else if (backslash) {
        filename = backslash + 1;
    } else {
        filename = filepath;
    }
    // --- 修正ここまで ---

    // ロード状態を初期化
    gMidiLoadingStatus.isLoading = true;
    gMidiLoadingStatus.hasError = false;
    gMidiLoadingStatus.progress = 0.0f;
    gMidiLoadingStatus.loadedTracks = 0;
    gMidiLoadingStatus.totalTracks = 0;
    strncpy(gMidiLoadingStatus.filename, filename, 255);
    gMidiLoadingStatus.filename[255] = '\0';
    updateLoadingStatus("Opening MIDI file...", 0.05f);  // Changed from "MIDIファイルを開いています..."

    pthread_t midiPlayer_play_thread;
    int midiPlayer_play_result = 0;

    // ヒープ上にMIDIFile構造体を確保
    MIDIFile *midiFile = malloc(sizeof(MIDIFile));
    if (!midiFile) {
        fprintf(stderr, "Error: メモリ確保に失敗しました\n");
        updateLoadingStatus("Error: Failed to allocate memory", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        return NULL;
    }

    // 構造体を初期化
    midiFile->TrackCount = 0;
    midiFile->Division = 0;
    midiFile->CurrentTick = 0;
    midiFile->Data = NULL;
    midiFile->DataLength = 0;
    midiFile->Tracks = NULL;
    midiFile->Running = true;

    // より詳細なファイル検証
    FILE *file = NULL;

    #ifdef _WIN32
    if (gUseWidePath) {
        // ワイド文字パスを使ってファイルを開く
        file = _wfopen(gWideFilePath, L"rb");
        if (!file) {
            fprintf(stderr, "Error: could not open file using wide path (error: %d)\n", errno);
            // 通常のパスでフォールバック
            file = fopen(filename, "rb");
        }
    } else {
        // 通常の方法でファイルを開く
        file = fopen(filename, "rb");
    }
    #else
    // 非Windows環境では通常通り
    file = fopen(filename, "rb");
    #endif

    if (!file) {
        fprintf(stderr, "Error: could not open file %s\n", filename);
        updateLoadingStatus("Error: Could not open file", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        free(midiFile);
        return NULL;
    }

    updateLoadingStatus("Parsing MIDI header...", 0.1f);  // Changed

    // ファイルの先頭16バイトをダンプして内容を確認（デバッグ用）
    fprintf(stderr, "File header dump: ");
    unsigned char header_dump[16] = {0};
    if (fread(header_dump, 1, 16, file) == 16) {
        for (int i = 0; i < 16; i++) {
            fprintf(stderr, "%02X ", header_dump[i]);
        }
        fprintf(stderr, "\n");

        // ASCII表示も追加
        fprintf(stderr, "ASCII: ");
        for (int i = 0; i < 16; i++) {
            fprintf(stderr, "%c", (header_dump[i] >= 32 && header_dump[i] < 127) ? header_dump[i] : '.');
        }
        fprintf(stderr, "\n");
    }
    fseek(file, 0, SEEK_SET);  // ファイルポインタを先頭に戻す

    unsigned long fileSize;
    fseek(file, 0, SEEK_END);
    fileSize = ftell(file);
    fseek(file, 0, SEEK_SET);

    // ファイルサイズのチェック
    if (fileSize < sizeof(MidiHeader)) {
        fprintf(stderr, "Error: File too small to be a valid MIDI file\n");
        updateLoadingStatus("Error: File too small", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        free(midiFile);
        return NULL;
    }

    // ヘッダー処理部分の改善
    MidiHeader header;
    if (fread(&header, sizeof(MidiHeader), 1, file) != 1) {
        fprintf(stderr, "Error: Failed to read MIDI header\n");
        updateLoadingStatus("Error: Failed to read MIDI header", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        free(midiFile);
        return NULL;
    }

    fprintf(stderr, "-- Header\n");
    fprintf(stderr, "ChunkID: %s\n", header.chunkID);

    // ChunkIDの検証（文字列として有効か確認）
    if (header.chunkID[0] != 'M' || header.chunkID[1] != 'T' ||
        header.chunkID[2] != 'h' || header.chunkID[3] != 'd') {
        fprintf(stderr, "Error: %s is not a valid MIDI file (invalid header signature)\n", filename);
        updateLoadingStatus("Error: Invalid MIDI header signature", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        free(midiFile);
        return NULL;
    }

    // エンディアン変換前の生のデータを表示（デバッグ用）
    fprintf(stderr, "Raw chunk size: %u\n", header.chunkSize);
    fprintf(stderr, "Raw format type: %u\n", header.formatType);
    fprintf(stderr, "Raw track count: %u\n", header.numberOfTracks);
    fprintf(stderr, "Raw time division: %u\n", header.timeDivision);

    // バイトオーダー変換
    header.chunkSize = swap_uint32(header.chunkSize);
    header.formatType = swap_uint16(header.formatType);
    header.numberOfTracks = swap_uint16(header.numberOfTracks);
    header.timeDivision = swap_uint16(header.timeDivision);

    // 合計トラック数を設定
    gMidiLoadingStatus.totalTracks = header.numberOfTracks;
    updateLoadingStatus("Loading track data...", 0.2f);  // Changed

    fprintf(stderr, "After swap - chunk size: %u\n", header.chunkSize);
    fprintf(stderr, "After swap - format type: %u\n", header.formatType);
    fprintf(stderr, "After swap - track count: %u\n", header.numberOfTracks);
    fprintf(stderr, "After swap - time division: %u\n", header.timeDivision);

    // チャンクサイズの検証を緩和（標準的なサイズではない場合も許容）
    if (header.chunkSize != 6) {
        fprintf(stderr, "Warning: Non-standard header length: %u (expected 6), continuing anyway\n", header.chunkSize);
    }

    // フォーマットタイプの検証を緩和
    if (header.formatType > 2) {
        fprintf(stderr, "Warning: Unusual format type: %u (expected 0, 1, or 2), attempting to process anyway\n", header.formatType);
    }

    if (header.timeDivision > 0x8000) {
        fprintf(stderr, "Warning: SMTPE time code detected - may not be fully supported\n");
    }

    // トラック数のサニティチェックの強化
    if (header.numberOfTracks <= 0) {
        fprintf(stderr, "Error: No tracks found in the MIDI file\n");
        updateLoadingStatus("Error: No tracks in MIDI file", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        free(midiFile);
        return NULL;
    }
    else if (header.numberOfTracks > 10000) {  // 上限値を大幅に引き上げ
        fprintf(stderr, "Warning: File claims to have %d tracks, this is unusually high\n", header.numberOfTracks);
    }

    fprintf(stderr, "FormatType: %d\n", header.formatType);
    fprintf(stderr, "TrackSize: %d\n", header.numberOfTracks);
    fprintf(stderr, "Division: %d\n", header.timeDivision);

    // データサイズの計算方法を改善
    // ファイルサイズから直接計算
    unsigned long dataSize = fileSize - sizeof(MidiHeader);
    fprintf(stderr, "- Estimated data size: %lu bytes (%0.2f MB)\n", dataSize, dataSize / (1024.0 * 1024.0));

    char sizeMsg[256];
    sprintf(sizeMsg, "Data size: %.2f MB - Allocating memory...", dataSize / (1024.0 * 1024.0));  // Changed
    updateLoadingStatus(sizeMsg, 0.25f);

    midiFile->Division = header.timeDivision;
    midiFile->TrackCount = header.numberOfTracks;
    midiFile->CurrentTick = 0;

    // 特に大きなファイルのためのメモリ割り当て改善
    midiFile->Data = malloc(dataSize);
    if (!midiFile->Data) {
        // メモリ割り当て失敗の場合、詳細なエラーメッセージを提供
        fprintf(stderr, "Error: Failed to allocate %lu bytes (%0.2f MB) for MIDI data: %s\n",
                dataSize, dataSize / (1024.0 * 1024.0), strerror(errno));
        updateLoadingStatus("Error: Failed to allocate memory", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        fclose(file);
        free(midiFile);
        return NULL;
    }
    midiFile->DataLength = dataSize;

    size_t tracksMemSize = sizeof(Track) * midiFile->TrackCount;
    fprintf(stderr, "- Allocating %zu bytes for %d tracks\n", tracksMemSize, midiFile->TrackCount);

    // トラック数の現実的な上限を設定
    if (midiFile->TrackCount > 10000) {
        fprintf(stderr, "Warning: Very high track count (%d) detected, limiting to 1000 tracks\n", midiFile->TrackCount);
        midiFile->TrackCount = 1000; // より現実的な上限に制限
        tracksMemSize = sizeof(Track) * midiFile->TrackCount;
    }

    midiFile->Tracks = (Track *)malloc(tracksMemSize);
    if (!midiFile->Tracks) {
        fprintf(stderr, "Error: Failed to allocate memory for MIDI tracks: %s\n", strerror(errno));
        updateLoadingStatus("Error: Failed to allocate track memory", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        free(midiFile->Data);
        free(midiFile);
        fclose(file);
        return NULL;
    }

    // トラック配列の初期化を明示的に行う
    memset(midiFile->Tracks, 0, tracksMemSize);

    midiFile->Running = true;

    fseek(file, 14, SEEK_SET); // ヘッダーの後に移動

    // intからunsigned long longに変更して大きなファイルを扱えるようにする
    unsigned long long offset = 0;

    updateLoadingStatus("Parsing track data...", 0.3f);  // Changed

    // ヘッダー情報から有効なトラック数を記録するための変数を追加
    int validTrackCount = 0;

    for (int i = 0; i < header.numberOfTracks && i < midiFile->TrackCount; i++)
    {
        // トラック毎の進行状況を更新
        float trackProgress = 0.3f + 0.5f * ((float)i / header.numberOfTracks);
        char trackMsg[256];
        sprintf(trackMsg, "Processing track %d/%d...", i+1, header.numberOfTracks);  // Changed
        updateLoadingStatus(trackMsg, trackProgress);
        gMidiLoadingStatus.loadedTracks = i;

        // 最初にトラックを「終了済み」としてマーク、有効な場合のみ変更
        midiFile->Tracks[i].Ended = true;

        MidiTrack track;

        // ファイル位置の妥当性確認
        long currentPos = ftell(file);
        if (currentPos < 0 || (unsigned long)currentPos >= fileSize) {
            fprintf(stderr, "Warning: Invalid file position at track %d, stopping process\n", i);
            break;
        }

        // トラックヘッダーを読み込む前にファイル終端チェック
        if (currentPos + sizeof(MidiTrack) > fileSize) {
            fprintf(stderr, "Warning: Reached end of file at track %d, processing what we have so far\n", i);
            break;
        }

        size_t bytesRead = fread(&track, 1, sizeof(MidiTrack), file);
        if (bytesRead < sizeof(MidiTrack)) {
            fprintf(stderr, "Warning: Could not read complete track header for track %d, got %zu bytes\n", i, bytesRead);
            break;
        }

        fprintf(stderr, "-- Track %d\n", i);
        fprintf(stderr, "ChunkID: %s\n", track.chunkID);

        // サイズ制限のチェック - 2GBを超えるファイルへの対応
        if (offset > 1800000000ULL) { // 約1.8GBでカットオフ（安全マージン）
            fprintf(stderr, "Warning: Total data size approaching 2GB limit, stopping at track %d\n", i);
            fprintf(stderr, "Current total size: %llu bytes\n", offset);
            break;
        }

        // Track検証コードの改良
        if (track.chunkID[0] != 'M' || track.chunkID[1] != 'T' ||
            track.chunkID[2] != 'r' || track.chunkID[3] != 'k') {
            fprintf(stderr, "Warning: Track %d has invalid chunk ID '%.4s', attempting to recover\n",
                   i, track.chunkID);

            // MThdパターンの検出 - 連結ファイルの可能性
            if (track.chunkID[0] == 'M' && track.chunkID[1] == 'T' &&
                track.chunkID[2] == 'h' && track.chunkID[3] == 'd') {
                fprintf(stderr, "Found another MThd header - this might be a concatenated file\n");
                fprintf(stderr, "Processing tracks read so far as a complete MIDI file\n");
                // 現在のMIDIファイルとして処理を終了し、読み込んだトラックで処理を続行
                break;
            }

            // チャンクIDが無効な場合、次のMTrk検索を試みる
            char buffer[4];
            int found = 0;
            long position = ftell(file) - sizeof(MidiTrack);

            while ((unsigned long)(position + 4) < fileSize && !found) {
                fseek(file, position, SEEK_SET);
                if (fread(buffer, 1, 4, file) == 4) {
                    if (buffer[0] == 'M' && buffer[1] == 'T' &&
                        buffer[2] == 'r' && buffer[3] == 'k') {
                        found = 1;
                        fprintf(stderr, "Found MTrk signature at offset %ld\n", position);
                        fseek(file, position, SEEK_SET);
                        bytesRead = fread(&track, 1, sizeof(MidiTrack), file);
                        if (bytesRead != sizeof(MidiTrack)) {
                            fprintf(stderr, "Error: Failed to read track header after recovery\n");
                            break;
                        }
                    } else if (buffer[0] == 'M' && buffer[1] == 'T' &&
                              buffer[2] == 'h' && buffer[3] == 'd') {
                        // 別のMIDIファイルのヘッダーを発見
                        fprintf(stderr, "Found new MThd header at offset %ld - concatenated file detected\n", position);
                        fprintf(stderr, "Processing tracks read so far as a complete MIDI file\n");
                        found = 2; // 特別なフラグ: 現在のファイルの処理を終了
                        break;
                    }
                }
                position++;
            }

            if (found == 2) {
                // 新しいMThdが見つかった場合、現在のファイルの処理を終了
                break;
            }

            if (!found) {
                fprintf(stderr, "Error: Failed to recover from invalid chunk ID\n");
                continue;
            }
        }

        track.chunkSize = swap_uint32(track.chunkSize);

        // トラックサイズの検証を強化・緩和
        if (track.chunkSize <= 0) {
            fprintf(stderr, "Warning: Track %d has zero size, skipping\n", i);
            continue;
        }

        // 大きなトラックサイズも許容
        if (track.chunkSize > 500 * 1024 * 1024) { // 500MB以上のトラックでも処理を試みる
            fprintf(stderr, "Warning: Track %d has very large size: %u bytes (%0.2f MB)\n",
                   i, track.chunkSize, track.chunkSize / (1024.0 * 1024.0));
        }

        // トラックデータ読み込み前のバッファサイズチェックを強化
        if (offset + track.chunkSize > dataSize) {
            fprintf(stderr, "Warning: Track %d data would exceed buffer size, attempting to resize\n", i);

            // データサイズが異常に大きい場合はスキップ
            if (track.chunkSize > 100 * 1024 * 1024) { // 100MB以上のトラック
                fprintf(stderr, "Warning: Track %d has unusually large size (%u bytes), skipping\n",
                        i, track.chunkSize);
                continue;
            }

            // バッファサイズを拡張
            unsigned long newSize = offset + track.chunkSize + (1024 * 1024); // 余裕を持って拡張
            fprintf(stderr, "Attempting to resize buffer to %lu bytes (%0.2f MB)\n",
                    newSize, newSize / (1024.0 * 1024.0));

            uint8_t *newData = realloc(midiFile->Data, newSize);
            if (!newData) {
                fprintf(stderr, "Error: Failed to resize buffer: %s\n", strerror(errno));
                fprintf(stderr, "Will process with available data up to track %d\n", i);
                break;
            }

            midiFile->Data = newData;
            midiFile->DataLength = newSize;
            dataSize = newSize;
            fprintf(stderr, "Buffer successfully resized\n");
        }

        // 有効なトラックのみ設定
        midiFile->Tracks[validTrackCount].head = &midiFile->Data[offset];
        midiFile->Tracks[validTrackCount].RunningStatus = 0;
        midiFile->Tracks[validTrackCount].Tick = 0;
        midiFile->Tracks[validTrackCount].Ended = false;

        // ポインタの設定を先に行い、その後データを読み込む
        // セグメンテーション違反を防ぐためのバッファサイズチェック
        if ((long long)offset + (long long)track.chunkSize > (long long)midiFile->DataLength) {
            fprintf(stderr, "Error: Track would exceed allocated buffer size\n");
            break;
        }

        // 正しいメモリ位置にデータを読み込む
        bytesRead = fread(&midiFile->Data[offset], 1, track.chunkSize, file);

        if (bytesRead != track.chunkSize)
        {
            fprintf(stderr, "Error: could not read track data, expected %u bytes, got %zu\n",
                   track.chunkSize, bytesRead);
            // 部分的に読み込めた場合でも処理を試みる
            if (bytesRead > 0) {
                fprintf(stderr, "Warning: Will try to process partially read track data\n");
                // ポインタが有効であることを再確認
                fprintf(stderr, "Track %d head pointer: %p, Data base: %p, offset: %llu\n",
                       validTrackCount, (void*)midiFile->Tracks[validTrackCount].head,
                       (void*)midiFile->Data, offset);
                offset += bytesRead;
                validTrackCount++; // 部分的に読み込めたトラックもカウント
            } else {
                break;
            }
        } else {
            // ポインタが有効であることを再確認
            fprintf(stderr, "Track %d head pointer: %p, Data base: %p, offset: %llu\n",
                   validTrackCount, (void*)midiFile->Tracks[validTrackCount].head,
                   (void*)midiFile->Data, offset);
            offset += bytesRead;
            validTrackCount++; // 有効なトラック数をカウント
            gMidiLoadingStatus.loadedTracks = validTrackCount;
            fprintf(stderr, "Track %d size: %u bytes\n", i, (unsigned int)bytesRead);
        }

        fprintf(stderr, "Total read: %llu bytes (%0.2f GB)\n",
                offset, offset / (1024.0 * 1024.0 * 1024.0));
    }

    // 実際の有効なトラック数をTrackCountに設定
    midiFile->TrackCount = validTrackCount;

    updateLoadingStatus("Validating track pointers...", 0.85f);  // Changed

    // 実際に読み込んだトラック数を確認
    if (offset == 0 || midiFile->TrackCount == 0) {
        fprintf(stderr, "Error: No valid tracks found in MIDI file\n");
        updateLoadingStatus("Error: No valid tracks found", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        free(midiFile->Tracks);
        free(midiFile->Data);
        free(midiFile);
        fclose(file);
        return NULL;
    }

    // トラックヘッドポインタの検証部分の強化
    fprintf(stderr, "Validating track pointers...\n");
    int validPointers = 0;
    for (int i = 0; i < validTrackCount; i++) {
        // NULLチェックを追加
        if (midiFile->Tracks[i].head == NULL) {
            fprintf(stderr, "Warning: Track %d has NULL pointer, marking as ended\n", i);
            midiFile->Tracks[i].Ended = true;
            continue;
        }

        // ポインタの範囲チェックを強化
        uintptr_t headPtr = (uintptr_t)midiFile->Tracks[i].head;
        uintptr_t dataStart = (uintptr_t)midiFile->Data;
        uintptr_t dataEnd = dataStart + midiFile->DataLength;

        if (headPtr >= dataStart && headPtr < dataEnd) {
            validPointers++;
        } else {
			fprintf(stderr, "Warning: Track %d has invalid pointer (0x%llx), valid range: 0x%llx-0x%llx, marking as ended\n",
				i, (unsigned long long)headPtr, (unsigned long long)dataStart, (unsigned long long)dataEnd);
            midiFile->Tracks[i].Ended = true;
        }
    }
    fprintf(stderr, "Found %d valid track pointers out of %d tracks\n", validPointers, validTrackCount);

    // 有効なポインタが全くない場合は処理を中止
    if (validPointers == 0) {
        fprintf(stderr, "Error: No valid track pointers found\n");
        updateLoadingStatus("Error: No valid track pointers", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        free(midiFile->Tracks);
        free(midiFile->Data);
        free(midiFile);
        fclose(file);
        return NULL;
    }

    updateLoadingStatus("Preparing playback thread...", 0.9f);  // Changed

    // clearPrerenderedData();  // Prerendering disabled

    // トラック数を最大トラック数とダウンロードしたデータ量に基づいて制限
	// トラック数無限
    unsigned int maxTracks = midiFile->TrackCount;
    if ((unsigned int)midiFile->TrackCount > maxTracks) {
        fprintf(stderr, "Warning: Limiting playback to first %d tracks (out of %d)\n",
                maxTracks, midiFile->TrackCount);
        midiFile->TrackCount = maxTracks;
    }

    struct midiPlayer_play_args *mp_args = malloc(sizeof(struct midiPlayer_play_args));
    mp_args->midiFile = midiFile;  // ヒープ上の構造体へのポインタを渡す

    updateLoadingStatus("Starting playback thread...", 0.95f);  // Changed

    // ファイルロード前にテンポマップを初期化
    initTempoMap();

    // テンポ変更イベントをスキャンしてテンポマップを構築
    scanTempoEventsInMidiFile(midiFile);

    // prerenderMidiFileNotes(midiFile);  // Prerendering disabled

    // 総時間を計算して保存
    gMidiLoadingStatus.totalTimeMs = getMidiTotalTimeMs();

    // スキャン完了後、ノート数をカウント（テンポスキャンの後に追加）
    countNotesInMidiFile(midiFile);

    // BPM情報を出力
    float minBpm = 0.0f, maxBpm = 0.0f, avgBpm = 0.0f;
    getMidiBpmInfo(&minBpm, &maxBpm, &avgBpm);
    fprintf(stderr, "MIDI BPM Info - Min: %.2f, Max: %.2f, Avg: %.2f\n",
            minBpm, maxBpm, avgBpm);

    // 外部アクセス用関数を呼び出して時間計算用の比率を初期化
    initializeTimeCalculation();

    // 読み込み完了後に初期化関数を呼び出す
    initializePlaybackState();

    midiPlayer_play_result = pthread_create(&midiPlayer_play_thread, NULL, playMidiFile, mp_args);

    if (midiPlayer_play_result != 0)
    {
        updateLoadingStatus("Error: Failed to start playback thread", 0.0f);  // Changed
        gMidiLoadingStatus.hasError = true;
        gMidiLoadingStatus.isLoading = false;
        free(mp_args);
        free(midiFile->Tracks);
        free(midiFile->Data);
        free(midiFile);
        fclose(file);
        return NULL;
    }

    // MIDIファイルロード完了
    updateLoadingStatus("MIDI file loading completed", 1.0f);  // Changed

    // 全体時間計算部分を削除
    gMidiLoadingStatus.totalTimeMs = 0;

    // ロード完了後にウィンドウタイトル更新フラグを設定（直接更新ではなく）
    extern void setWindowTitleUpdateFlag(const char* filename);
    extern void updateBpmInfo();  // BPM情報更新関数の参照を追加
    setWindowTitleUpdateFlag(gMidiLoadingStatus.filename);

    // ロード完了時にBPM情報を更新
    updateBpmInfo();

    // ロード完了シグナルを少し残してから消す
    struct timespec ts;
    ts.tv_sec = 1;  // 1秒間表示
    ts.tv_nsec = 0;
    nanosleep(&ts, NULL);

    gMidiLoadingStatus.isLoading = false;

    pthread_join(midiPlayer_play_thread, NULL);

    // スレッド終了後にクリーンアップ
    free(mp_args);
    free(midiFile->Tracks);
    free(midiFile->Data);
    free(midiFile);
    fclose(file);

    fprintf(stderr, "MIDI file loaded and played successfully\n");
    return NULL;
}

// 既に player.c で定義されている変数の参照
extern MIDIFile *gMidiFile;
// Add these empty function implementations at the end of the file

// Empty implementation to replace prerendering functionality
void clearPrerenderedData(void) {
    // This function is intentionally empty as prerendering has been removed
    fprintf(stderr, "Note: Prerendering disabled - clearPrerenderedData() does nothing\n");
}

// Empty implementation to replace prerendering functionality
void prerenderMidiFileNotes(MIDIFile *midiFile) {
    // This function is intentionally empty as prerendering has been removed
    // Suppress unused parameter warning
    (void)midiFile;
    fprintf(stderr, "Note: Prerendering disabled - prerenderMidiFileNotes() does nothing\n");
}
