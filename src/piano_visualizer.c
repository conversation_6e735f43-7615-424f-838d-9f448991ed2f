#include "piano_visualizer.h"
#include <raylib.h>
#include <raymath.h>
#include "rlgl.h"
#include <stdlib.h>
#include "setting_window.h"  // 設定ウィンドウのヘッダーを追加
#include "midiplayer.h"  // 一時停止/再生機能のためのヘッダーを追加
#include "camera.h"  // 新しいカメラモジュールを追加

// グローバル変数の定義
NoteVisual noteHistory[MAX_NOTE_HISTORY];
int nextNoteIndex = 0;
KeyState keyStates[TOTAL_KEYS];

// ピアノキーの変数
int whiteKeyWidth;
int whiteKeyHeight;
int blackKeyWidth;
int blackKeyHeight;
int keyboardStartX;
int keyboardStartY;

// 白鍵と黒鍵のパターン (オクターブ内での位置)
// 0 = 白鍵, 1 = 黒鍵
const int KEY_PATTERN[OCTAVE_NOTES] = {0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0};

// 外部から定義されたグローバル変数への参照
extern Color channelColors[16];
extern int channelPitchBends[16];

// 各チャンネルのピッチベンド値を保持する配列 (0-16383, 中央値=8192)
int channelPitchBends[16] = {
    8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192,
    8192, 8192, 8192, 8192, 8192, 8192, 8192, 8192
};

// ピッチベンド値を更新する関数（外部から呼び出し用）
void updateChannelPitchBend(int channel, int value) {
    if (channel >= 0 && channel < 16) {
        // 値を更新
        channelPitchBends[channel] = value;
    }
}

// 白鍵の総数を計算
int countWhiteKeys() {
    int whiteKeyCount = 0;
    for (int note = LOWEST_NOTE; note < LOWEST_NOTE + TOTAL_KEYS; note++) {
        int octavePosition = note % OCTAVE_NOTES;
        if (KEY_PATTERN[octavePosition] == 0) {
            whiteKeyCount++;
        }
    }
    return whiteKeyCount;
}

// ピアノキーのサイズをウィンドウサイズに合わせて更新
void updatePianoSizes() {
    int screenWidth = GetScreenWidth();
    int screenHeight = GetScreenHeight();

    // 白鍵の数を取得
    int totalWhiteKeys = countWhiteKeys();

    // キーボードの幅はウィンドウの幅の28%に設定（70%から60%小さく）
    int keyboardWidth = screenWidth * 0.28;
    keyboardStartX = (screenWidth - keyboardWidth) / 2; // 中央に配置

    // ピアノの高さはウィンドウ高さの8%に設定（20%から60%小さく）
    whiteKeyHeight = screenHeight * 0.08;
    blackKeyHeight = whiteKeyHeight * 0.6;

    // 白鍵の幅を計算
    whiteKeyWidth = keyboardWidth / totalWhiteKeys;
    blackKeyWidth = whiteKeyWidth * 0.6;

    // キーボードの垂直位置
    keyboardStartY = screenHeight - whiteKeyHeight - 20; // 下部に20pxの余白
}

// ノートの水平位置（X座標）を計算する関数
int getNoteX(int note) {
    // 白鍵のカウント
    int whiteKeysBefore = 0;
    for (int i = LOWEST_NOTE; i < note; i++) {
        if (KEY_PATTERN[i % OCTAVE_NOTES] == 0) {
            whiteKeysBefore++;
        }
    }

    // 現在のノートが白鍵か黒鍵かを判断
    if (KEY_PATTERN[note % OCTAVE_NOTES] == 0) {
        // 白鍵の場合
        return keyboardStartX + whiteKeysBefore * whiteKeyWidth + whiteKeyWidth / 2;
    } else {
        // 黒鍵の場合 - 直前の白鍵の右側に配置
        return keyboardStartX + whiteKeysBefore * whiteKeyWidth - blackKeyWidth / 2 + whiteKeyWidth;
    }
}

// ノートの幅を取得する関数
int getNoteWidth(int note) {
    if (KEY_PATTERN[note % OCTAVE_NOTES] == 0) {
        return whiteKeyWidth - 2;  // 白鍵の幅（境界線のスペースを引く）
    } else {
        return blackKeyWidth - 2;  // 黒鍵の幅
    }
}

// ノートデータを更新する関数（2Dと3D共通で使用）- フレームレート非依存版
void updateNoteData() {
    // 一時停止中は位置更新しない（表示は維持）
    if (isMidiPaused()) return;

    static float accumulator = 0.0f;
    float deltaTime = GetFrameTime();
    accumulator += deltaTime;

    // 更新間隔として0.033秒（約30FPS相当）を目安に
    // 前回の更新からの累積時間が閾値を超えたら更新処理を行う
    if (accumulator >= 0.033f) {
        float timeFactor = accumulator; // 累積時間で位置更新を調整
        accumulator = 0.0f; // 累積時間をリセット

        // 既存のノートの位置を更新 - 累積時間に基づいて一定速度で移動
        for (int i = 0; i < MAX_NOTE_HISTORY; i++) {
            if (noteHistory[i].used) {
                // 位置を更新（累積時間に基づいて）
                noteHistory[i].position += NOTE_SPEED * timeFactor;

                // 画面外のノートを無効化（しきい値を小さく設定）
                if (noteHistory[i].position > NOTE_CLEANUP_THRESHOLD && !noteHistory[i].active) {
                    noteHistory[i].used = false;  // スロットを解放
                }
            }
        }
    }

    float currentTime = GetTime();

    // gVisualizerのmutexは一度だけロック
    pthread_mutex_lock(&gVisualizer.mutex);

    // アクティブノート検出の効率化 - 使用済みスロットの再利用
    for (int ch = 0; ch < 16; ch++) {
        for (int note = 0; note < 128; note++) {
            if (gVisualizer.notes[ch][note].active) {
                // 既存のアクティブノートを高速に検索
                bool exists = false;
                for (int i = MAX_NOTE_HISTORY - 100; i < MAX_NOTE_HISTORY; i++) {
                    if (noteHistory[i].used &&
                        noteHistory[i].note == note &&
                        noteHistory[i].channel == ch &&
                        noteHistory[i].active) {
                        exists = true;
                        break;
                    }
                }

                if (!exists) {
                    // 未使用スロットを探す - 検索範囲を制限
                    int emptySlot = nextNoteIndex;
                    nextNoteIndex = (nextNoteIndex + 1) % MAX_NOTE_HISTORY;

                    noteHistory[emptySlot].note = note;
                    noteHistory[emptySlot].channel = ch;
                    noteHistory[emptySlot].position = 0;
                    noteHistory[emptySlot].startTime = currentTime;
                    noteHistory[emptySlot].active = true;
                    noteHistory[emptySlot].used = true;
                    noteHistory[emptySlot].endPosition = 0;
                }
            } else {
                // ノートオフ処理の効率化 - 検索範囲を制限
                for (int i = MAX_NOTE_HISTORY - 100; i < MAX_NOTE_HISTORY; i++) {
                    if (noteHistory[i].used &&
                        noteHistory[i].note == note &&
                        noteHistory[i].channel == ch &&
                        noteHistory[i].active) {
                        noteHistory[i].active = false;
                        noteHistory[i].endPosition = noteHistory[i].position;
                    }
                }
            }
        }
    }
    pthread_mutex_unlock(&gVisualizer.mutex);
}

// キーの状態を初期化する関数
void initKeyStates() {
    for (int i = 0; i < TOTAL_KEYS; i++) {
        keyStates[i].targetHeight = 0;
        keyStates[i].currentHeight = 0;
        keyStates[i].colorBlend = 0;
        keyStates[i].isActive = false;
        keyStates[i].lastChannel = 0;
    }
}

// キーの状態を更新する関数 - フレームレート非依存版
void updateKeyStates() {
    // 一時停止中は状態更新しない（現在の表示状態を維持）
    if (isMidiPaused()) return;

    static float accumulator = 0.0f;
    float deltaTime = GetFrameTime();
    accumulator += deltaTime;

    // キー状態の滑らかさを保つために、より高いレートで更新する (0.016秒 ≈ 60FPS)
    if (accumulator >= 0.016f) {
        float timeFactor = accumulator; // 累積時間で遷移速度を調整
        accumulator = 0.0f; // 累積時間をリセット

        // 時間に基づく遷移速度
        float heightTransitionSpeed = 15.0f * timeFactor;
        float colorFadeSpeed = 5.0f * timeFactor;

        pthread_mutex_lock(&gVisualizer.mutex);
        // すべてのキーの状態を更新するのではなく、アクティブなキーだけを更新
        for (int ch = 0; ch < 16; ch++) {
            for (int note = 0; note < 128; note++) {
                if (gVisualizer.notes[ch][note].active) {
                    // アクティブなノートは即座に状態更新
                    if (!keyStates[note].isActive) {
                        keyStates[note].isActive = true;
                        keyStates[note].targetHeight = 1.0f;
                        keyStates[note].colorBlend = 1.0f;
                        keyStates[note].lastChannel = ch;
                    }
                } else if (keyStates[note].isActive) {
                    // アクティブから非アクティブになった場合のみ状態更新
                    bool stillActive = false;
                    for (int c = 0; c < 16; c++) {
                        if (gVisualizer.notes[c][note].active) {
                            stillActive = true;
                            break;
                        }
                    }

                    if (!stillActive) {
                        keyStates[note].isActive = false;
                        keyStates[note].targetHeight = 0.0f;
                    }
                }
            }
        }
        pthread_mutex_unlock(&gVisualizer.mutex);

        // 現在の高さと色を目標値に向けて更新
        for (int note = 0; note < TOTAL_KEYS; note++) {
            if (keyStates[note].currentHeight != keyStates[note].targetHeight) {
                // デルタタイム補正Lerpで滑らかに遷移
                keyStates[note].currentHeight = Lerp(keyStates[note].currentHeight,
                                                    keyStates[note].targetHeight,
                                                    heightTransitionSpeed);
            }

            if (!keyStates[note].isActive && keyStates[note].colorBlend > 0.0f) {
                // デルタタイム補正でフェードアウト
                keyStates[note].colorBlend = Clamp(keyStates[note].colorBlend - colorFadeSpeed, 0.0f, 1.0f);
            }
        }
    }
}

// 3Dピアノキーボードを描画する関数 - 最適化版
void draw3DPianoKeyboard() {
    // キーのサイズは毎フレームでなく、ウィンドウサイズ変更時のみ更新
    static int lastScreenWidth = 0;
    static int lastScreenHeight = 0;

    int currentWidth = GetScreenWidth();
    int currentHeight = GetScreenHeight();

    if (currentWidth != lastScreenWidth || currentHeight != lastScreenHeight) {
        updatePianoSizes();
        lastScreenWidth = currentWidth;
        lastScreenHeight = currentHeight;
    }

    // 一時停止中でない場合のみキー状態を更新
    if (!isMidiPaused()) {
        updateKeyStates();
    }

    // スケールファクターを導入して全体を小さくする（先に定義）
    float scaleX = 1.0f;  // X方向のスケール（横幅）
    float scaleY = 1.0f;  // Y方向のスケール（高さ）
    float scaleZ = 1.0f;  // Z方向のスケール（奥行き）

	int whiteKeyCount = 0;
	float keyboardWidth = whiteKeyWidth * countWhiteKeys();
	float offsetX = -keyboardWidth * scaleX / 2;  // X方向の中心を0に
	float offsetY = -20.0f;  // Y軸基準点を少し下げる
	float offsetZ = 0.0f;  // Z方向の中心を調整（カメラの視点に合わせる）

    // ピッチベンドによる最大移動量（スケール適用後のピクセル単位）
    float maxPitchBendMove = 256.0f * scaleX;

    // キーボード全体のピッチベンド値を決定
    float globalPitchBendOffset = 0.0f;
    int totalActiveNotes = 0;

    // 各チャンネルのアクティブノート数をカウント
    pthread_mutex_lock(&gVisualizer.mutex);
    int activeNotesPerChannel[16] = {0};

    for (int ch = 0; ch < 16; ch++) {
        for (int note = 0; note < 128; note++) {
            if (gVisualizer.notes[ch][note].active) {
                activeNotesPerChannel[ch]++;
                totalActiveNotes++;
            }
        }
    }
    pthread_mutex_unlock(&gVisualizer.mutex);

    // ピッチベンドが有効な場合のみ適用
    if (enablePitchBend && totalActiveNotes > 0) {
        for (int ch = 0; ch < 16; ch++) {
            // アクティブなノートがあるチャンネルのみピッチベンドを適用
            if (activeNotesPerChannel[ch] > 0) {
                float weight = (float)activeNotesPerChannel[ch] / totalActiveNotes;
                float pitchBendFactor = (channelPitchBends[ch] - 8192) / 8192.0f;
                globalPitchBendOffset += pitchBendFactor * maxPitchBendMove * weight;
            }
        }
    }

    // グラデーションキューブを描画する関数 - よりスムーズなバージョン
    void DrawGradientCube(Vector3 position, float width, float height, float length,
                          Color baseColor, float yGradientFactor, float zGradientFactor) {
        // 分割数を増やしてグラデーションをよりスムーズに
        const int ySegments = 8;  // 高さ方向の分割数（5から8に増加）
        const int zSegments = 20; // 奥行き方向の分割数（5から10に増加）

        float segmentHeight = height / ySegments;
        float segmentLength = length / zSegments;

        for (int y = 0; y < ySegments; y++) {
            // 二次関数的なグラデーションのために二乗を使用
            float yRatio = (float)y / (ySegments - 1);
            float yFactor = 1.0f - (yRatio * yRatio); // 二乗カーブでより自然な明るさ変化
            float yPos = position.y - height / 2 + segmentHeight * (y + 0.5);

            for (int z = 0; z < zSegments; z++) {
                // 奥から手前への滑らかな変化
                float zRatio = (float)z / (zSegments - 1);
                float zFactor = 1.0f - (zRatio * zRatio); // 二乗カーブでより自然な明るさ変化
                float zPos = position.z - length / 2 + segmentLength * (z + 0.5);

                // 奥行きと高さに基づいてカラーグラデーションを計算
                Color segmentColor = baseColor;

                // Y方向のグラデーション（よりスムーズに）
                float yBlend = yGradientFactor * yFactor;

                // Z方向のグラデーション（よりスムーズに）
                float zBlend = zGradientFactor * zFactor;

                // 両方の効果を組み合わせて自然な光の効果に
                float totalBrightness = 1.0f + yBlend + zBlend;

                segmentColor.r = (unsigned char)Clamp(segmentColor.r * totalBrightness, 0, 255);
                segmentColor.g = (unsigned char)Clamp(segmentColor.g * totalBrightness, 0, 255);
                segmentColor.b = (unsigned char)Clamp(segmentColor.b * totalBrightness, 0, 255);

                // セグメントキューブを描画
                DrawCube((Vector3){ position.x, yPos, zPos }, width, segmentHeight, segmentLength, segmentColor);
            }
        }
    }

    // 白鍵を先に描画
    for (int note = LOWEST_NOTE; note < LOWEST_NOTE + TOTAL_KEYS; note++) {
        int octavePosition = note % OCTAVE_NOTES;
        if (KEY_PATTERN[octavePosition] == 0) {
            // 白鍵の位置を計算 - キーボード全体のピッチベンドオフセットを適用
            float x = offsetX + whiteKeyCount * whiteKeyWidth * scaleX + globalPitchBendOffset;
            float z = offsetZ;

            // ノートがアクティブかチェック
            bool isActive = false;
            int noteChannel = -1;

            pthread_mutex_lock(&gVisualizer.mutex);
            for (int ch = 0; ch < 16; ch++) {
                if (gVisualizer.notes[ch][note].active) {
                    isActive = true;
                    noteChannel = ch;
                    break;
                }
            }
            pthread_mutex_unlock(&gVisualizer.mutex);

            // 白鍵の高さ（スムーズな移行）
            float normalHeight = 3.0f * scaleY;
            float pressedHeight = 1.5f * scaleY;
            float transitionHeight = normalHeight - (normalHeight - pressedHeight) * keyStates[note].currentHeight;

            // キーの色を計算（チャンネル色と白の間でブレンド）
            Color keyColor = (Color){ 150, 150, 150, 255 };  // デフォルトは白
            if (keyStates[note].colorBlend > 0.0f) {
                int blendChannel = isActive ? noteChannel : keyStates[note].lastChannel;
                if (blendChannel >= 0 && blendChannel < 16) {
                    Color channelColor = channelColors[blendChannel];
                    // ブレンド係数を使用して色を補間
                    keyColor = (Color){
                        (unsigned char)Lerp(keyColor.r, channelColor.r, keyStates[note].colorBlend),
                        (unsigned char)Lerp(keyColor.g, channelColor.g, keyStates[note].colorBlend),
                        (unsigned char)Lerp(keyColor.b, channelColor.b, keyStates[note].colorBlend),
                        255  // 不透明度は常に最大
                    };
                }
            }

            // 通常のキューブではなくグラデーションキューブを描画
            Vector3 keyPosition = { x + whiteKeyWidth * scaleX / 2, offsetY + transitionHeight / 2, z + whiteKeyHeight * scaleZ / 2 };
            // 基本のグラデーション強度（押されていなくても適用）+ 押された時の追加強度
            float baseGradient = 0.2f;  // 基本のグラデーション強度
            float pressedGradient = keyStates[note].colorBlend * 0.5f;  // 押されたときの追加強度
            float gradientFactor = baseGradient + pressedGradient;

            DrawGradientCube(keyPosition,
                            whiteKeyWidth * scaleX,
                            transitionHeight,
                            whiteKeyHeight * 0.6f * scaleZ,
                            keyColor,
                            gradientFactor,  // Y方向のグラデーション強度
                            gradientFactor * 1.5f);  // Z方向のグラデーション強度（より強め）

            // DrawCubeWires((Vector3){ x + whiteKeyWidth * scaleX / 2, offsetY + transitionHeight / 2, z + whiteKeyHeight * scaleZ / 2 },
            //              whiteKeyWidth * scaleX, transitionHeight, whiteKeyHeight * 0.6f * scaleZ, BLACK);

            whiteKeyCount++;
        }
    }

    // 黒鍵を後から描画して重ねる
    whiteKeyCount = 0;
    for (int note = LOWEST_NOTE; note < LOWEST_NOTE + TOTAL_KEYS; note++) {
        int octavePosition = note % OCTAVE_NOTES;
        if (KEY_PATTERN[octavePosition] == 0) {
            whiteKeyCount++;
        } else {
            // 黒鍵の位置を計算 - キーボード全体のピッチベンドオフセットを適用
            float x = offsetX + (whiteKeyCount - 1) * whiteKeyWidth * scaleX + whiteKeyWidth * scaleX - blackKeyWidth * scaleX / 2 + globalPitchBendOffset;
            // 黒鍵のZ座標を白鍵よりやや奥に配置する（白鍵よりも奥側に少し出る）
            float z = offsetZ + (whiteKeyHeight * 0.4f) * scaleZ;

            // ノートがアクティブかチェック
            bool isActive = false;
            int noteChannel = -1;

            pthread_mutex_lock(&gVisualizer.mutex);
            for (int ch = 0; ch < 16; ch++) {
                if (gVisualizer.notes[ch][note].active) {
                    isActive = true;
                    noteChannel = ch;
                    break;
                }
            }
            pthread_mutex_unlock(&gVisualizer.mutex);

            // 黒鍵の高さ（スムーズな移行）- 通常時は白鍵より高くする
            float normalHeight = 6.0f * scaleY;  // 白鍵よりも高く設定（10.0fから12.0fに変更）
            float pressedHeight = 4.5f * scaleY;
            float transitionHeight = normalHeight - (normalHeight - pressedHeight) * keyStates[note].currentHeight;

            // キーの色を計算（チャンネル色と黒の間でブレンド）
            Color keyColor = BLACK;  // デフォルトは黒
            if (keyStates[note].colorBlend > 0.0f) {
                int blendChannel = isActive ? noteChannel : keyStates[note].lastChannel;
                if (blendChannel >= 0 && blendChannel < 16) {
                    Color channelColor = channelColors[blendChannel];
                    // ブレンド係数を使用して色を補間
                    keyColor = (Color){
                        (unsigned char)Lerp(BLACK.r, channelColor.r, keyStates[note].colorBlend),
                        (unsigned char)Lerp(BLACK.g, channelColor.g, keyStates[note].colorBlend),
                        (unsigned char)Lerp(BLACK.b, channelColor.b, keyStates[note].colorBlend),
                        255  // 不透明度は常に最大
                    };
                }
            }

            // 黒鍵を描画 - グローバルピッチベンドオフセット適用済み
            Vector3 blackKeyPosition = { x + blackKeyWidth * scaleX / 2, offsetY + transitionHeight / 2, z };
            // 黒鍵も基本グラデーション + 押されたときの追加強度
            float blackBaseGradient = 0.15f;  // 黒鍵の基本グラデーション（白鍵より少し控えめ）
            float blackPressedGradient = keyStates[note].colorBlend * 0.4f;
            float blackGradientFactor = blackBaseGradient + blackPressedGradient;

            DrawGradientCube(blackKeyPosition,
                            blackKeyWidth * scaleX,
                            transitionHeight,
                            blackKeyHeight * 0.6f * scaleZ,
                            keyColor,
                            blackGradientFactor,  // Y方向のグラデーション強度
                            blackGradientFactor * 1.2f);  // Z方向のグラデーション強度

            // DrawCubeWires((Vector3){ x + blackKeyWidth * scaleX / 2, offsetY + transitionHeight / 2, z },
            //               blackKeyWidth * scaleX, transitionHeight, blackKeyHeight * 0.6f * scaleZ, DARKGRAY);
        }
    }
}

// ピアノビジュアライザーの状態をリセットする関数の実装
void resetPianoVisualizer() {
    // キー状態をリセット
    for (int i = 0; i < TOTAL_KEYS; i++) {
        keyStates[i].targetHeight = 0;
        keyStates[i].currentHeight = 0;
        keyStates[i].colorBlend = 0;
        keyStates[i].isActive = false;
        keyStates[i].lastChannel = 0;
    }

    // ノート履歴をリセット
    for (int i = 0; i < MAX_NOTE_HISTORY; i++) {
        noteHistory[i].used = false;
        noteHistory[i].active = false;
    }

    // 次のノートインデックスをリセット
    nextNoteIndex = 0;

    // ピッチベンドをリセット
    for (int i = 0; i < 16; i++) {
        channelPitchBends[i] = 8192; // 中央値にリセット
    }
}

// プリレンダリング関連の関数を空の実装に置き換え
void initNotePrerendering(void) {
    // 機能を無効化
}

void clearPrerenderedNotes(void) {
    // 機能を無効化
}

bool importExternalPrerenderingData(void) {
    // 機能を無効化
    return false;
}

void sortPrerenderedNotes(void) {
    // 機能を無効化
}

void preloadNotesFromMidi(int startTimeMs, int durationMs) {
    // Acknowledge unused parameters to prevent compiler warnings
    (void)startTimeMs;
    (void)durationMs;

    // Function is disabled - does nothing
}

void updatePrerenderedNotes(void) {
    // 機能を無効化
}

void drawPianoRollFromPrerenderedNotes(void) {
    // 代替の表示または空の実装
    int screenWidth = GetScreenWidth();
    int screenHeight = GetScreenHeight();

    // ピアノロールのサイズと位置を計算 - 適切な位置に表示
    int pianoRollWidth = screenWidth * 0.75;
    int pianoRollHeight = screenHeight * 0.5;
    int pianoRollX = (screenWidth - pianoRollWidth) / 2;
    int pianoRollY = screenHeight * 0.05;

    // 背景を描画して見やすくする
    DrawRectangle(pianoRollX, pianoRollY, pianoRollWidth, pianoRollHeight,
                 (Color){20, 20, 30, 200});  // 半透明の暗い背景
    DrawRectangleLines(pianoRollX, pianoRollY, pianoRollWidth, pianoRollHeight,
                      (Color){150, 150, 150, 255});  // 枠線

    // メッセージを表示
    const char* message = "プリレンダリング機能は無効化されました";
    int textWidth = MeasureText(message, 20);
    DrawText(message,
            pianoRollX + (pianoRollWidth - textWidth) / 2,
            pianoRollY + pianoRollHeight / 2 - 10,
            20, WHITE);
}

// ダミー関数の実装
int getPrerenderedNoteCount(void) {
    return 0;
}

int getActivePrerenderedNoteCount(void) {
    return 0;
}
