#include "keyboard_display.h"
#include "piano_visualizer.h" // 色の取得用
#include "raygui_impl.h"
#include <stdio.h>
#include <string.h>  // strncpy関数の宣言のために追加
#include "setting_window.h" // enableKeyboardDisplayにアクセスするために追加
#include "status_overlay.h" // ステータスオーバーレイの高さを取得するために追加
#include "midiplayer.h"    // getCurrentInstrumentName関数へのアクセス用

// キーボード表示のデフォルト設定
bool showKeyboardDisplay = false;
bool noteState[16][128] = {0};

// piano_visualizer.cで定義されたグローバル変数への参照
extern Color channelColors[16];

// 楽器変更の検出とハイライト表示のための変数
static uint8_t lastProgramNumbers[16] = {0};  // 前回のプログラム番号
static float instrumentChangeTimer[16] = {0}; // ハイライト効果のタイマー
static const float HIGHLIGHT_DURATION = 2.0f; // ハイライト効果の持続時間（秒）
static char lastInstrumentNames[16][64] = {0}; // 前回の楽器名を保存
static bool animatingInstrumentChange[16] = {false}; // 楽器変更アニメーション中かどうか

// キーボード表示設定（シンプルなフラットデザイン用に最適化）
static KeyboardDisplayConfig config = {
    .keyWidth = 3,          // 鍵盤の幅をさらに小さくしてスペース効率を上げる
    .whiteKeyHeight = 15,   // 白鍵の高さを小さく
    .blackKeyHeight = 15,   // 黒鍵の高さも小さく
    .channelHeight = 15,    // チャンネルの高さも小さく
    .startNote = 0,         // 最低音のMIDIノート番号（128鍵の全範囲をカバー）
    .endNote = 127          // 最高音のMIDIノート番号（128鍵の全範囲をカバー）
};

// 黒鍵かどうかを判定
static bool isBlackKey(int note) {
    int noteInOctave = note % 12;
    return (noteInOctave == 1 || noteInOctave == 3 || noteInOctave == 6 ||
            noteInOctave == 8 || noteInOctave == 10);
}

// 初期化関数
void initKeyboardDisplay(void) {
    // ノート状態の初期化
    for (int ch = 0; ch < 16; ch++) {
        for (int note = 0; note < 128; note++) {
            noteState[ch][note] = false;
        }

        // 楽器変更検出のための変数も初期化
        lastProgramNumbers[ch] = getCurrentProgram(ch);
        instrumentChangeTimer[ch] = 0.0f;
        animatingInstrumentChange[ch] = false;
        strncpy(lastInstrumentNames[ch], getCurrentInstrumentName(ch), 63);
        lastInstrumentNames[ch][63] = '\0';
    }
}

// チャンネルの色を取得
Color getChannelColor(int channel) {
    if (channel >= 0 && channel < 16) {
        return channelColors[channel];
    }
    return WHITE;
}

// ノートオン処理
void keyboardNoteOn(int channel, int note) {
    if (channel >= 0 && channel < 16 && note >= 0 && note < 128) {
        noteState[channel][note] = true;
    }
}

// ノートオフ処理
void keyboardNoteOff(int channel, int note) {
    if (channel >= 0 && channel < 16 && note >= 0 && note < 128) {
        noteState[channel][note] = false;
    }
}

// 16チャンネル分のキーボードを描画（シンプルなフラットデザイン）
void drawKeyboardDisplay(void) {
    // enableKeyboardDisplayがfalseの場合は表示しない
    if (!enableKeyboardDisplay) return;

    float deltaTime = GetFrameTime(); // フレーム時間を取得（アニメーション用）

    int screenWidth = GetScreenWidth();
    // 未使用変数を削除
    // int screenHeight = GetScreenHeight();

    // プログレスバーの高さを取得
    int progressBarHeight = 20;

    // ステータスオーバーレイの高さを取得
    int statusOverlayHeight = getStatusOverlayHeight();

    // プログレスバーとステータスオーバーレイの下に表示するよう位置を調整
    int startY = progressBarHeight + statusOverlayHeight; // 余白を5pxに減らして上部スペースを節約

    // 全てのキー（0-127）を表示するために幅を計算
    int totalWidth = (config.endNote - config.startNote + 1) * config.keyWidth;

    // 表示起点のX座標
    int startX = (screenWidth - totalWidth) / 2;

    // 楽器名表示のための変数
    int instrumentNameX = startX + totalWidth + 10; // キーボードの右側に10px余白を追加
    int instrumentNameWidth = 160; // 楽器名の最大表示幅
    int fontSize = 10; // フォントサイズを小さめに設定

    // キーボードの幅が大きすぎる場合は右端に表示できるよう調整
    if (instrumentNameX + instrumentNameWidth > screenWidth) {
        instrumentNameX = screenWidth - instrumentNameWidth - 5;
    }

    // 各チャンネルのキーボードを描画
    for (int ch = 0; ch < 16; ch++) {
        int channelY = startY + ch * config.channelHeight;

        // まず白鍵を描画（枠線なしのフラットデザイン）
        for (int note = config.startNote; note <= config.endNote; note++) {
            if (!isBlackKey(note)) {
                int x = startX + (note - config.startNote) * config.keyWidth;

                // 画面内に表示される場合のみ描画
                if (x + config.keyWidth >= 0 && x < screenWidth) {
                    Rectangle keyRect = {x, channelY, config.keyWidth, config.whiteKeyHeight};

                    // 鍵盤が押されている場合はチャンネルの色、それ以外は白（少し暗め）で描画
                    if (noteState[ch][note]) {
                        DrawRectangleRec(keyRect, getChannelColor(ch));
                    } else {
                        DrawRectangleRec(keyRect, (Color){220, 220, 220, 255}); // やや暗めの白
                    }
                }
            }
        }

        // 次に黒鍵を描画（枠線なしのフラットデザイン）
        for (int note = config.startNote; note <= config.endNote; note++) {
            if (isBlackKey(note)) {
                int x = startX + (note - config.startNote) * config.keyWidth;

                // 画面内に表示される場合のみ描画
                if (x + config.keyWidth >= 0 && x < screenWidth) {
                    // 黒鍵は白鍵の上に重ねて少し小さめにする
                    Rectangle keyRect = {x, channelY, config.keyWidth, config.blackKeyHeight};

                    // 鍵盤が押されている場合はチャンネルの色、それ以外は黒で描画
                    if (noteState[ch][note]) {
                        DrawRectangleRec(keyRect, getChannelColor(ch));
                    } else {
                        DrawRectangleRec(keyRect, (Color){40, 40, 40, 255}); // 濃いめの黒
                    }
                }
            }
        }

        // 楽器名を表示（チャンネルの色で）
        const char* instrumentName = getCurrentInstrumentName(ch);
        Color textColor = getChannelColor(ch);
        uint8_t currentProgram = getCurrentProgram(ch);

        // 楽器が変更されたかチェック
        bool programChanged = (currentProgram != lastProgramNumbers[ch]);
        if (programChanged) {
            // 古い楽器名を保存
            strncpy(lastInstrumentNames[ch], getCurrentInstrumentName(ch), 63);
            lastInstrumentNames[ch][63] = '\0';

            instrumentChangeTimer[ch] = HIGHLIGHT_DURATION; // タイマーをリセット
            lastProgramNumbers[ch] = currentProgram; // 新しいプログラム番号を保存
            animatingInstrumentChange[ch] = true; // アニメーション中フラグをセット
        }

        // タイマー更新（0未満にならないように）
        if (instrumentChangeTimer[ch] > 0) {
            instrumentChangeTimer[ch] -= deltaTime;
            if (instrumentChangeTimer[ch] < 0) {
                instrumentChangeTimer[ch] = 0;
                animatingInstrumentChange[ch] = false; // アニメーション終了
            }
        }

        // チャンネル番号と楽器名を表示
        char channelText[200];
        snprintf(channelText, sizeof(channelText), "Ch%2d: %s", ch+1, instrumentName);

        // チャンネルで何かノートがアクティブなら不透明度を上げる
        bool hasActiveNotes = false;
        for (int note = 0; note < 128; note++) {
            if (noteState[ch][note]) {
                hasActiveNotes = true;
                break;
            }
        }

        // アクティブなチャンネルは不透明度100%、非アクティブは60%
        textColor.a = hasActiveNotes ? 255 : 180;

        // テキスト描画位置を調整（チャンネルの高さの中央に配置）- 先に宣言
        int textY = channelY + (config.channelHeight - fontSize) / 2;

        // 楽器変更があった場合のハイライトエフェクト
        if (instrumentChangeTimer[ch] > 0) {
            // ハイライト背景色（フェードアウト効果付き）
            Color highlightColor = getChannelColor(ch);
            highlightColor.a = (unsigned char)((instrumentChangeTimer[ch] / HIGHLIGHT_DURATION) * 80);

            // テキストの背景にハイライト効果を描画
            float textWidth = MeasureText(channelText, fontSize);
            Rectangle highlightRect = {
                instrumentNameX - 2,
                channelY,
                textWidth + 4,
                config.channelHeight
            };
            DrawRectangleRec(highlightRect, highlightColor);

            // アニメーション中の場合
            if (animatingInstrumentChange[ch]) {
                // アニメーションの進行度（0.0～1.0）
                float progress = 1.0f - (instrumentChangeTimer[ch] / HIGHLIGHT_DURATION);

                // チャンネル部分のテキスト
                char channelPrefix[10];
                snprintf(channelPrefix, sizeof(channelPrefix), "Ch%2d: ", ch+1);
                int prefixWidth = MeasureText(channelPrefix, fontSize);

                // 古い楽器名と新しい楽器名の幅を取得
                float oldNameWidth = MeasureText(lastInstrumentNames[ch], fontSize);
                float newNameWidth = MeasureText(instrumentName, fontSize);
                float maxNameWidth = oldNameWidth > newNameWidth ? oldNameWidth : newNameWidth;

                // 古い楽器名の位置と不透明度を計算（右へスライド）
                int oldTextX = instrumentNameX + prefixWidth + (int)(maxNameWidth * progress);
                Color oldTextColor = textColor;
                oldTextColor.a = (unsigned char)((1.0f - progress) * textColor.a);

                // 新しい楽器名の位置と不透明度を計算（左からスライド）
                int newTextX = instrumentNameX + prefixWidth - (int)(maxNameWidth * (1.0f - progress));
                Color newTextColor = textColor;
                newTextColor.a = (unsigned char)(progress * textColor.a);

                // チャンネル番号部分を描画
                DrawText(channelPrefix, instrumentNameX, textY, fontSize, textColor);

                // 古い楽器名を描画（右へスライドアウト）
                DrawText(lastInstrumentNames[ch], oldTextX, textY, fontSize, oldTextColor);

                // 新しい楽器名を描画（左からスライドイン）
                DrawText(instrumentName, newTextX, textY, fontSize, newTextColor);

                // アニメーション中は標準のテキスト描画をスキップ
                continue;
            }

            // 変更直後は文字を太字風に
            if (instrumentChangeTimer[ch] > HIGHLIGHT_DURATION * 0.7f) {
                // 少しずらして描画することで太字効果
                DrawText(channelText, instrumentNameX+1, textY, fontSize, textColor);
            }
        }

        // 標準のテキスト描画 (textYはすでに宣言済み)
        DrawText(channelText, instrumentNameX, textY, fontSize, textColor);
    }

    // オクターブの区切りを薄い線で表示（12音ごと）
    for (int octave = 0; octave <= 10; octave++) {
        int note = octave * 12;
        if (note >= config.startNote && note <= config.endNote) {
            int x = startX + (note - config.startNote) * config.keyWidth;
            DrawLine(x, startY, x, startY + 16 * config.channelHeight, (Color){100, 100, 100, 100});
        }
    }
}
