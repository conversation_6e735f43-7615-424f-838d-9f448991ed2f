// WindowsとRaylibの競合を解決
#include "raylib_compat.h"

// 他のインクルード
#include "midi_visualizer.h"
#include "keyboard_display.h"  // Header for keyboard display
#include <string.h>
#include <stdlib.h>
#include <stdio.h>  // Added for fprintf and stderr
#include <time.h>

// Add NPS entry structure
typedef struct {
    int notes;
    uint64_t timestamp;
} NPSEntry;

#define MAX_NPS_ENTRIES 1000

// Global visualizer instance
MidiVisualizer gVisualizer;

// Global variables for statistics
static struct {
    int totalNotes;         // Total number of notes played (NOTE ON events only)
    int notesCount;         // Number of NOTE ON events
    int polyphony;          // Maximum simultaneous notes
    float notesPerSecond;   // Current NPS
    NPSEntry npsEntries[MAX_NPS_ENTRIES]; // NPS tracking entries
    int npsEntryCount;      // Number of NPS entries
    int notesThisTick;      // Notes in the current tick
    int currentNotes;       // Current active notes (simultaneous notes)
} noteStats = {
    .totalNotes = 0,
    .notesCount = 0,
    .polyphony = 0,
    .notesPerSecond = 0.0f,
    .npsEntries = {{0}},
    .npsEntryCount = 0,
    .notesThisTick = 0,
    .currentNotes = 0
};

void setTotalNotes(int count) {
    // Log changes only if there is a difference
    int previousCount = noteStats.totalNotes;

    // Set total number of notes calculated from NOTE ON events
    noteStats.totalNotes = count;

    fprintf(stderr, "Total notes set from MIDI file: %d (previous: %d)\n",
            count, previousCount);
}

// Initialize visualizer
void initVisualizer() {
    // Do nothing if already initialized
    if (gVisualizer.initialized) return;

    // Initialize note state array
    memset(gVisualizer.notes, 0, sizeof(gVisualizer.notes));

    // Initialize mutex
    pthread_mutex_init(&gVisualizer.mutex, NULL);

    // Initialize time
    gVisualizer.currentTime = 0;

    // Mark initialization as complete
    gVisualizer.initialized = true;

    // Reset statistics
    noteStats.notesCount = 0;
    noteStats.notesThisTick = 0;
    noteStats.notesPerSecond = 0.0f;
    noteStats.currentNotes = 0;
    noteStats.polyphony = 0;
    noteStats.npsEntryCount = 0;
    memset(noteStats.npsEntries, 0, sizeof(noteStats.npsEntries));
}

// Cleanup visualizer
void cleanupVisualizer() {
    if (!gVisualizer.initialized) return;

    pthread_mutex_destroy(&gVisualizer.mutex);
    gVisualizer.initialized = false;
}

// Process NOTE ON event - parameter type changed to uint8_t
void visualizerNoteOn(uint8_t channel, uint8_t note, uint8_t velocity) {
    if (channel > 15 || note > 127) return;
    if (!gVisualizer.initialized) return;

    pthread_mutex_lock(&gVisualizer.mutex);

    // Do not increase count if the same note is already active
    // (but update the status)
    bool wasAlreadyActive = gVisualizer.notes[channel][note].active;

    // Update note information
    gVisualizer.notes[channel][note].active = true;
    gVisualizer.notes[channel][note].velocity = velocity;
    gVisualizer.notes[channel][note].startTime = gVisualizer.currentTime;
    gVisualizer.notes[channel][note].endTime = 0;
    gVisualizer.notes[channel][note].needsUpdate = true;

    noteStats.notesCount++;

    // Increment notes for current tick
    noteStats.notesThisTick++;

    // Update simultaneous notes count (only if the same note was not already active)
    if (!wasAlreadyActive) {
        noteStats.currentNotes++;

        // Update maximum simultaneous notes
        if (noteStats.currentNotes > noteStats.polyphony) {
            noteStats.polyphony = noteStats.currentNotes;
        }
    }

    pthread_mutex_unlock(&gVisualizer.mutex);

    // Update NOTE ON state for keyboard display
    keyboardNoteOn(channel, note);
}

// Process NOTE OFF event
void visualizerNoteOff(uint8_t channel, uint8_t note) {
    if (!gVisualizer.initialized) return;
    if (channel > 15 || note > 127) return;

    pthread_mutex_lock(&gVisualizer.mutex);

    // Decrease simultaneous notes count only if the note was actually active
    if (gVisualizer.notes[channel][note].active) {
        noteStats.currentNotes--;
        // Protect against negative values
        if (noteStats.currentNotes < 0) {
            noteStats.currentNotes = 0;
        }
    }

    // Set note to inactive
    gVisualizer.notes[channel][note].active = false;
    gVisualizer.notes[channel][note].endTime = gVisualizer.currentTime;

    pthread_mutex_unlock(&gVisualizer.mutex);

    // Update NOTE OFF state for keyboard display
    keyboardNoteOff(channel, note);
}

// Update current time
void visualizerUpdateTime(uint64_t time) {
    if (!gVisualizer.initialized) return;

    pthread_mutex_lock(&gVisualizer.mutex);
    gVisualizer.currentTime = time;
    pthread_mutex_unlock(&gVisualizer.mutex);
}

// Get current time
uint64_t getVisualizerTime() {
    if (!gVisualizer.initialized) return 0;

    uint64_t time;
    pthread_mutex_lock(&gVisualizer.mutex);
    time = gVisualizer.currentTime;
    pthread_mutex_unlock(&gVisualizer.mutex);

    return time;
}

// Reset visualizer state
void resetMidiVisualizer() {
    if (!gVisualizer.initialized) return;

    pthread_mutex_lock(&gVisualizer.mutex);

    // Reset all note states
    for (int ch = 0; ch < 16; ch++) {
        for (int note = 0; note < 128; note++) {
            // Reset active notes only (inactive notes remain unchanged)
            if (gVisualizer.notes[ch][note].active) {
                gVisualizer.notes[ch][note].active = false;
            }
        }
    }

    // Reset current note count
    noteStats.currentNotes = 0;
    noteStats.notesPerSecond = 0.0f;
    noteStats.notesThisTick = 0;

    pthread_mutex_unlock(&gVisualizer.mutex);
}

// Calculate polyphony (number of simultaneous notes) - directly return currentNotes as it's already managed
int getCurrentPolyphony(void) {
    // currentNotes is thread-safe, so we can return it directly
    return noteStats.currentNotes;
}

// Get total number of notes played
int getTotalNotes(void) {
    return noteStats.totalNotes;
}

// Get current active note count - same as current polyphony
int getCurrentNotes(void) {
    return noteStats.currentNotes;
}

// Get count of NOTE ON events
int getNotesCount(void) {
    return noteStats.notesCount;
}

// Calculate notes per second (NPS) - completely rewritten
float getNotesPerSecond(void) {
    uint64_t currentTime = gVisualizer.currentTime;
    uint64_t oneSecondAgo = (currentTime > 1000000000ULL) ? currentTime - 1000000000ULL : 0;

    pthread_mutex_lock(&gVisualizer.mutex);

    // Add new entry if we have notes in this tick
    if (noteStats.notesThisTick > 0) {
        // Shift array if needed
        if (noteStats.npsEntryCount >= MAX_NPS_ENTRIES) {
            memmove(&noteStats.npsEntries[0], &noteStats.npsEntries[1],
                    (MAX_NPS_ENTRIES - 1) * sizeof(NPSEntry));
            noteStats.npsEntryCount--;
        }

        // Add new entry
        noteStats.npsEntries[noteStats.npsEntryCount].notes = noteStats.notesThisTick;
        noteStats.npsEntries[noteStats.npsEntryCount].timestamp = currentTime;
        noteStats.npsEntryCount++;
        noteStats.notesThisTick = 0;
    }

    // Calculate NPS from entries within the last second
    float nps = 0.0f;
    int validEntries = 0;

    // Remove old entries and sum up notes
    int writeIdx = 0;
    for (int i = 0; i < noteStats.npsEntryCount; i++) {
        if (noteStats.npsEntries[i].timestamp >= oneSecondAgo) {
            if (writeIdx != i) {
                noteStats.npsEntries[writeIdx] = noteStats.npsEntries[i];
            }
            nps += noteStats.npsEntries[i].notes;
            validEntries++;
            writeIdx++;
        }
    }
    noteStats.npsEntryCount = writeIdx;

    pthread_mutex_unlock(&gVisualizer.mutex);

    return validEntries > 0 ? nps : 0.0f;
}
