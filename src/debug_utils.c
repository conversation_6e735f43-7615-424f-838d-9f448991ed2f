#include "debug_utils.h"
#include <stdarg.h>
#include <time.h>
#include <string.h>

#ifdef __linux__
#include <sys/utsname.h>
#include <unistd.h>
#include <sys/syscall.h>
#endif

#ifdef _WIN32
// Windows環境でUTF-8テキストを正しく出力する関数
void debug_print_utf8(const char* fmt, ...) {
    va_list args;
    va_start(args, fmt);

    // 一時バッファにフォーマット
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);

    // 現在のコードページを確認
    UINT cp = GetConsoleOutputCP();

    // コンソールの文字コードを取得
    if (cp == CP_UTF8) {
        // すでにUTF-8の場合は直接出力
        fprintf(stderr, "%s", buffer);
        fflush(stderr);
    } else {
        // UTF-8からコンソールコードページに変換
        // 必要なバッファサイズを取得
        int wide_size = MultiByteToWideChar(CP_UTF8, 0, buffer, -1, NULL, 0);
        if (wide_size > 0) {
            // ワイド文字に変換
            wchar_t* wide_buf = (wchar_t*)malloc(wide_size * sizeof(wchar_t));
            if (wide_buf) {
                MultiByteToWideChar(CP_UTF8, 0, buffer, -1, wide_buf, wide_size);

                // コンソール出力コードページに変換
                int mb_size = WideCharToMultiByte(cp, 0, wide_buf, -1, NULL, 0, NULL, NULL);
                if (mb_size > 0) {
                    char* mb_buf = (char*)malloc(mb_size);
                    if (mb_buf) {
                        WideCharToMultiByte(cp, 0, wide_buf, -1, mb_buf, mb_size, NULL, NULL);
                        fprintf(stderr, "%s", mb_buf);
                        fflush(stderr);
                        free(mb_buf);
                    }
                }
                free(wide_buf);
            }
        }
    }
}
#endif

#ifdef __linux__
// Linux固有のデバッグ情報を出力する関数
void debug_print_linux_info() {
    struct utsname sys_info;
    if (uname(&sys_info) == 0) {
        DEBUG_LOG("=== Linux System Debug Info ===");
        DEBUG_LOG("System: %s", sys_info.sysname);
        DEBUG_LOG("Node: %s", sys_info.nodename);
        DEBUG_LOG("Release: %s", sys_info.release);
        DEBUG_LOG("Version: %s", sys_info.version);
        DEBUG_LOG("Machine: %s", sys_info.machine);
        DEBUG_LOG("===============================");
    }

    // プロセス情報
    DEBUG_LOG("Process ID: %d", getpid());
    DEBUG_LOG("Thread ID: %ld", syscall(SYS_gettid));

    // 環境変数の確認
    const char* display = getenv("DISPLAY");
    const char* wayland = getenv("WAYLAND_DISPLAY");
    const char* desktop = getenv("XDG_CURRENT_DESKTOP");

    DEBUG_LOG("DISPLAY: %s", display ? display : "not set");
    DEBUG_LOG("WAYLAND_DISPLAY: %s", wayland ? wayland : "not set");
    DEBUG_LOG("XDG_CURRENT_DESKTOP: %s", desktop ? desktop : "not set");
}

// Linux固有のタイムスタンプ付きデバッグログ
void debug_log_with_timestamp_linux(const char* fmt, ...) {
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);

    struct tm* tm_info = localtime(&ts.tv_sec);
    char timestamp[64];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // ナノ秒も含める
    char full_timestamp[80];
    snprintf(full_timestamp, sizeof(full_timestamp), "%s.%09ld", timestamp, ts.tv_nsec);

    va_list args;
    va_start(args, fmt);

    fprintf(stderr, "[%s] [DEBUG] ", full_timestamp);
    vfprintf(stderr, fmt, args);
    fprintf(stderr, "\n");
    fflush(stderr);

    va_end(args);
}
#endif
