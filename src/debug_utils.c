#include "debug_utils.h"
#include <stdarg.h>

#ifdef _WIN32
// Windows環境でUTF-8テキストを正しく出力する関数
void debug_print_utf8(const char* fmt, ...) {
    va_list args;
    va_start(args, fmt);

    // 一時バッファにフォーマット
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);

    // 現在のコードページを確認
    UINT cp = GetConsoleOutputCP();

    // コンソールの文字コードを取得
    if (cp == CP_UTF8) {
        // すでにUTF-8の場合は直接出力
        fprintf(stderr, "%s", buffer);
        fflush(stderr);
    } else {
        // UTF-8からコンソールコードページに変換
        // 必要なバッファサイズを取得
        int wide_size = MultiByteToWideChar(CP_UTF8, 0, buffer, -1, NULL, 0);
        if (wide_size > 0) {
            // ワイド文字に変換
            wchar_t* wide_buf = (wchar_t*)malloc(wide_size * sizeof(wchar_t));
            if (wide_buf) {
                MultiByteToWideChar(CP_UTF8, 0, buffer, -1, wide_buf, wide_size);

                // コンソール出力コードページに変換
                int mb_size = WideCharToMultiByte(cp, 0, wide_buf, -1, NULL, 0, NULL, NULL);
                if (mb_size > 0) {
                    char* mb_buf = (char*)malloc(mb_size);
                    if (mb_buf) {
                        WideCharToMultiByte(cp, 0, wide_buf, -1, mb_buf, mb_size, NULL, NULL);
                        fprintf(stderr, "%s", mb_buf);
                        fflush(stderr);
                        free(mb_buf);
                    }
                }
                free(wide_buf);
            }
        }
    }
}
#endif
